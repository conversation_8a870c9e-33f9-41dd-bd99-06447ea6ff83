import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from parser import HanaInformationExtractor

# Simple test
extractor = HanaInformationExtractor()
result = extractor.extract_from_sql("CREATE PROCEDURE TEST() AS BEGIN SELECT * FROM VBAK; END;")

print(f"Success: {result.success}")
print(f"Method: {result.parsing_method}")
if result.procedure_info:
    print(f"Tables: {list(result.procedure_info.tables.keys())}")
    print(f"Filters: {len(result.procedure_info.all_filters)}")
    print(f"Joins: {len(result.procedure_info.all_joins)}")
