/****************************************************************************************
*
*   HANA SQLSCRIPT FILE FOR CELONIS ORDER-TO-CASH (O2C) DELTA EXTRACTION
*
*   Contains 21 stored procedures for extracting data from key SAP O2C tables.
*   Each procedure follows an idempotent delta-loading pattern with partitioning.
*
*   Execution: These procedures should be created in the HANA database.
*   They can be executed individually from a client tool like HANA Studio or DBeaver,
*   or called by an orchestration tool.
*
****************************************************************************************/


-- ======================================================================================
-- Procedure 1: Customer Master - General Data (KNA1)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_KNA1_CUSTOMER_MASTER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNA1_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNA1_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNA1_DELTA" (MANDT, KUNNR, LAND1, NAME1, ORT01, PSTLZ, REGIO, ERDAT, PARTITION_ID, CREATED_AT)
        SELECT MANDT, KUNNR, LAND1, NAME1, ORT01, PSTLZ, REGIO, ERDAT, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM KNA1 WHERE ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNA1_CHECKSUM" ("DATE_FROM", "DATE_TO", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 2: Customer Master - Company Code Data (KNB1)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_KNB1_CUSTOMER_CC_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNB1_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNB1_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNB1_DELTA" (MANDT, KUNNR, BUKRS, ERDAT, ZTERM, PARTITION_ID, CREATED_AT)
        SELECT MANDT, KUNNR, BUKRS, ERDAT, ZTERM, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM KNB1 WHERE ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND BUKRS = :IP_BUKRS;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KNB1_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 3: Material Master - General Data (MARA)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_MARA_MATERIAL_MASTER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_MARA_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_MARA_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_MARA_DELTA" (MANDT, MATNR, ERSDA, MTART, MATKL, MEINS, PARTITION_ID, CREATED_AT)
        SELECT MANDT, MATNR, ERSDA, MTART, MATKL, MEINS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM MARA WHERE ERSDA BETWEEN :IP_DATE_FROM AND :IP_DATE_TO;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_MARA_CHECKSUM" ("DATE_FROM", "DATE_TO", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 4: Sales Order Header (VBAK)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBAK_HEADER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAK_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAK_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAK_DELTA" (MANDT, VBELN, ERDAT, ERZET, ERNAM, AUART, VKORG, VTWEG, SPART, KUNNR, NETWR, WAERK, PARTITION_ID, CREATED_AT)
        SELECT MANDT, VBELN, ERDAT, ERZET, ERNAM, AUART, VKORG, VTWEG, SPART, KUNNR, NETWR, WAERK, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBAK WHERE ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAK_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 5: Sales Order Item (VBAP)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBAP_ITEM_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAP_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAP_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAP_DELTA" (MANDT, VBELN, POSNR, MATNR, ARKTX, ZIELM, ZMEIN, NETPR, NETWR, WERKS, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELN, T2.POSNR, T2.MATNR, T2.ARKTX, T2.KWMENG, T2.VRKME, T2.NETPR, T2.NETWR, T2.WERKS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBAK AS T1 INNER JOIN VBAP AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELN
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBAP_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 6: Sales Document: Header Status (VBUK)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBUK_HEADER_STATUS_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUK_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUK_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUK_DELTA" (MANDT, VBELN, LFSTK, WBSTK, FKSTK, GBSTK, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELN, T2.LFSTK, T2.WBSTK, T2.FKSTK, T2.GBSTK, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBAK AS T1 INNER JOIN VBUK AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELN
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUK_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 7: Sales Document: Item Status (VBUP)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBUP_ITEM_STATUS_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUP_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUP_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUP_DELTA" (MANDT, VBELN, POSNR, LFSTA, WBSTA, FKSTA, GBSTA, ABSTA, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELN, T2.POSNR, T2.LFSTA, T2.WBSTA, T2.FKSTA, T2.GBSTA, T2.ABSTA, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBAK AS T1 INNER JOIN VBUP AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELN
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBUP_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 8: Delivery Header (LIKP)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_LIKP_DELIVERY_HEADER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIKP_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIKP_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIKP_DELTA" (MANDT, VBELN, ERDAT, ERNAM, LFART, KUNNR, WADAT, VKORG, PARTITION_ID, CREATED_AT)
        SELECT MANDT, VBELN, ERDAT, ERNAM, LFART, KUNNR, WADAT, VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM LIKP WHERE ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIKP_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 9: Delivery Item (LIPS)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_LIPS_DELIVERY_ITEM_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIPS_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIPS_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIPS_DELTA" (MANDT, VBELN, POSNR, MATNR, ARKTX, LFIMG, VRKME, WERKS, VGBEL, VGPOS, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELN, T2.POSNR, T2.MATNR, T2.ARKTX, T2.LFIMG, T2.VRKME, T2.WERKS, T2.VGBEL, T2.VGPOS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM LIKP AS T1 INNER JOIN LIPS AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELN
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_LIPS_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 10: Shipment Header (VTTK)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VTTK_SHIPMENT_HEADER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_TPLST NVARCHAR(10))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTK_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "TPLST" = :IP_TPLST;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTK_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTK_DELTA" (MANDT, TKNUM, SHTYP, TPLST, ERDAT, ERNAM, STTRG, DTMEG, PARTITION_ID, CREATED_AT)
        SELECT MANDT, TKNUM, SHTYP, TPLST, ERDAT, ERNAM, STTRG, DTMEG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM VTTK WHERE ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND TPLST = :IP_TPLST;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTK_CHECKSUM" ("DATE_FROM", "DATE_TO", "TPLST", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_TPLST, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 11: Shipment Item (VTTP)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VTTP_SHIPMENT_ITEM_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_TPLST NVARCHAR(10))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTP_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "TPLST" = :IP_TPLST;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTP_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTP_DELTA" (MANDT, TKNUM, TPNUM, VBELN, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.TKNUM, T2.TPNUM, T2.VBELN, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VTTK AS T1 INNER JOIN VTTP AS T2 ON T1.MANDT = T2.MANDT AND T1.TKNUM = T2.TKNUM
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.TPLST = :IP_TPLST;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VTTP_CHECKSUM" ("DATE_FROM", "DATE_TO", "TPLST", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_TPLST, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 12: Billing Document Header (VBRK)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBRK_BILLING_HEADER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRK_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRK_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRK_DELTA" (MANDT, VBELN, FKART, FKTYP, BUKRS, KUNRG, FKDAT, NETWR, WAERK, SFAKN, PARTITION_ID, CREATED_AT)
        SELECT MANDT, VBELN, FKART, FKTYP, BUKRS_VF, KUNRG, FKDAT, NETWR, WAERK, SFAKN, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBRK WHERE FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND BUKRS_VF = :IP_BUKRS;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRK_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 13: Billing Document Item (VBRP)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBRP_BILLING_ITEM_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRP_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRP_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRP_DELTA" (MANDT, VBELN, POSNR, MATNR, FKIMG, VRKME, NETWR, VGBEL, VGPOS, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELN, T2.POSNR, T2.MATNR, T2.FKIMG, T2.VRKME, T2.NETWR, T2.VGBEL, T2.VGPOS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBRK AS T1 INNER JOIN VBRP AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELN
        WHERE T1.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.BUKRS_VF = :IP_BUKRS;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBRP_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 14: Accounting Document Header (BKPF)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_BKPF_ACCOUNTING_HEADER_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BKPF_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BKPF_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BKPF_DELTA" (MANDT, BUKRS, BELNR, GJAHR, BLART, BLDAT, BUDAT, USNAM, TCODE, AWKEY, PARTITION_ID, CREATED_AT)
        SELECT MANDT, BUKRS, BELNR, GJAHR, BLART, BLDAT, BUDAT, USNAM, TCODE, AWKEY, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM BKPF WHERE BUDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND BUKRS = :IP_BUKRS AND BLART IN ('RV', 'DR', 'DZ');
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BKPF_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 15: Accounting Document Segment (BSEG) -- FROM USER EXAMPLE
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_BSEG_ACTIVITY_LOAD"
    (IN IP_BUDAT_FROM NVARCHAR(8), IN IP_BUDAT_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSEG_CHECKSUM" WHERE "DATE_FROM" = :IP_BUDAT_FROM AND "DATE_TO" = :IP_BUDAT_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0')  INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSEG_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSEG_DELTA_ACTIVITIES" (MANDT, BUKRS, BELNR, GJAHR, BUZEI, TCODE, BUDAT, SHKZG, WRBTR, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.BUKRS, T2.BELNR, T2.GJAHR, T2.BUZEI, T1.TCODE, T2.BUDAT, T2.SHKZG, T2.WRBTR, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM BKPF AS T1 INNER JOIN BSEG AS T2 ON T1.MANDT = T2.MANDT AND T1.BUKRS = T2.BUKRS AND T1.BELNR = T2.BELNR AND T1.GJAHR = T2.GJAHR
        WHERE T2.BUDAT BETWEEN :IP_BUDAT_FROM AND :IP_BUDAT_TO AND T2.BUKRS = :IP_BUKRS AND T1.BLART IN ('RV', 'DR', 'DZ');
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSEG_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_BUDAT_FROM, :IP_BUDAT_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 16: Accounting: Customer Open Items (BSID)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_BSID_OPEN_ITEMS_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSID_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSID_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSID_DELTA" (MANDT, BUKRS, KUNNR, BELNR, GJAHR, BUZEI, BUDAT, DMBTR, SHKZG, ZUONR, PARTITION_ID, CREATED_AT)
        SELECT MANDT, BUKRS, KUNNR, BELNR, GJAHR, BUZEI, BUDAT, DMBTR, SHKZG, ZUONR, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM BSID WHERE BUDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND BUKRS = :IP_BUKRS;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSID_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 17: Accounting: Customer Cleared Items (BSAD)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_BSAD_CLEARED_ITEMS_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_BUKRS NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSAD_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "BUKRS" = :IP_BUKRS;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSAD_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSAD_DELTA" (MANDT, BUKRS, KUNNR, BELNR, GJAHR, AUGDT, AUGBL, DMBTR, SHKZG, PARTITION_ID, CREATED_AT)
        SELECT MANDT, BUKRS, KUNNR, BELNR, GJAHR, AUGDT, AUGBL, DMBTR, SHKZG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM BSAD WHERE AUGDT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND BUKRS = :IP_BUKRS;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_BSAD_CHECKSUM" ("DATE_FROM", "DATE_TO", "BUKRS", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_BUKRS, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 18: Document Flow (VBFA)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_VBFA_DOCUMENT_FLOW_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBFA_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBFA_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBFA_DELTA" (MANDT, VBELV, POSNV, VBELN, POSNN, VBTYP_N, VBTYP_V, ERDAT, ERZET, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDT, T2.VBELV, T2.POSNV, T2.VBELN, T2.POSNN, T2.VBTYP_N, T2.VBTYP_V, T2.ERDAT, T2.ERZET, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM VBAK AS T1 INNER JOIN VBFA AS T2 ON T1.MANDT = T2.MANDT AND T1.VBELN = T2.VBELV
        WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_VBFA_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 19: Change Log Header (CDHDR)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_CDHDR_CHANGES_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_TCODE NVARCHAR(20))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDHDR_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "TCODE" = :IP_TCODE;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDHDR_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDHDR_DELTA" (MANDANT, OBJECTCLAS, OBJECTID, CHANGENR, USERNAME, UDATE, UTIME, TCODE, PARTITION_ID, CREATED_AT)
        SELECT MANDANT, OBJECTCLAS, OBJECTID, CHANGENR, USERNAME, UDATE, UTIME, TCODE, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM CDHDR WHERE UDATE BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND OBJECTCLAS IN ('VERKBELEG', 'LIEFERUNG', 'FAKTURA') AND TCODE LIKE :IP_TCODE;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDHDR_CHECKSUM" ("DATE_FROM", "DATE_TO", "TCODE", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_TCODE, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 20: Change Log Item (CDPOS)
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_CDPOS_CHANGES_LOAD"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_TCODE NVARCHAR(20))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDPOS_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "TCODE" = :IP_TCODE;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDPOS_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDPOS_DELTA" (MANDANT, OBJECTCLAS, OBJECTID, CHANGENR, TABNAME, TABKEY, FNAME, VALUE_NEW, VALUE_OLD, PARTITION_ID, CREATED_AT)
        SELECT T2.MANDANT, T2.OBJECTCLAS, T2.OBJECTID, T2.CHANGENR, T2.TABNAME, T2.TABKEY, T2.FNAME, T2.VALUE_NEW, T2.VALUE_OLD, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP
        FROM CDHDR AS T1 INNER JOIN CDPOS AS T2 ON T1.MANDANT = T2.MANDANT AND T1.OBJECTCLAS = T2.OBJECTCLAS AND T1.OBJECTID = T2.OBJECTID AND T1.CHANGENR = T2.CHANGENR
        WHERE T1.UDATE BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.OBJECTCLAS IN ('VERKBELEG', 'LIEFERUNG', 'FAKTURA') AND T1.TCODE LIKE :IP_TCODE;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CDPOS_CHECKSUM" ("DATE_FROM", "DATE_TO", "TCODE", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_TCODE, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;

-- ======================================================================================
-- Procedure 21: Pricing Conditions (KONV) -- ENHANCED VERSION
-- ======================================================================================

PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_KONV_PRICING_LOAD_ENHANCED"
    (IN IP_DATE_FROM NVARCHAR(8), IN IP_DATE_TO NVARCHAR(8), IN IP_VKORG NVARCHAR(4))
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
    DECLARE V_PARTITION_ID NVARCHAR(10);
    DECLARE V_EXISTS INTEGER;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN SELECT ::SQL_ERROR_CODE || ':' || ::SQL_ERROR_MESSAGE FROM DUMMY; END;
    SELECT COUNT(*) INTO V_EXISTS FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KONV_CHECKSUM" WHERE "DATE_FROM" = :IP_DATE_FROM AND "DATE_TO" = :IP_DATE_TO AND "VKORG" = :IP_VKORG;
    IF V_EXISTS = 0 THEN
        SELECT LPAD(CAST(COALESCE(MAX(PARTITION_ID), '**********') + 1 AS NVARCHAR), 10, '0') INTO V_PARTITION_ID FROM "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KONV_CHECKSUM";
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KONV_DELTA" (MANDT, KNUMV, KPOSN, STUNR, ZAEHK, KSCHL, KWERT, KINAK, SOURCE_DOC_TYPE, SOURCE_DOC, PARTITION_ID, CREATED_AT)
        WITH KONV_DATA AS (
            -- 1. Get pricing data from Sales Orders (VBAK)
            SELECT T2.MANDT, T2.KNUMV, T2.KPOSN, T2.STUNR, T2.ZAEHK, T2.KSCHL, T2.KWERT, T2.KINAK, 'SALES_ORDER' AS SOURCE_DOC_TYPE, T1.VBELN AS SOURCE_DOC
            FROM VBAK AS T1 INNER JOIN KONV AS T2 ON T1.MANDT = T2.MANDT AND T1.KNUMV = T2.KNUMV
            WHERE T1.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG
            UNION ALL
            -- 2. Get pricing data from Billing Documents (VBRK)
            SELECT T2.MANDT, T2.KNUMV, T2.KPOSN, T2.STUNR, T2.ZAEHK, T2.KSCHL, T2.KWERT, T2.KINAK, 'BILLING_DOC' AS SOURCE_DOC_TYPE, T1.VBELN AS SOURCE_DOC
            FROM VBRK AS T1 INNER JOIN KONV AS T2 ON T1.MANDT = T2.MANDT AND T1.KNUMV = T2.KNUMV
            WHERE T1.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO AND T1.VKORG = :IP_VKORG
        )
        SELECT DISTINCT MANDT, KNUMV, KPOSN, STUNR, ZAEHK, KSCHL, KWERT, KINAK, SOURCE_DOC_TYPE, SOURCE_DOC, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP FROM KONV_DATA;
        INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_KONV_CHECKSUM" ("DATE_FROM", "DATE_TO", "VKORG", "PARTITION_ID", "LAST_DTM") VALUES (:IP_DATE_FROM, :IP_DATE_TO, :IP_VKORG, :V_PARTITION_ID, CURRENT_UTCTIMESTAMP);
    END IF;
    COMMIT;
END;