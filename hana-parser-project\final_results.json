{"extraction_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 30, "parameter_count": 0, "complexity_score": 96, "parsing_method": "FALLBACK", "tables": ["KEY", "NVARCHAR", "DUMMY", "AND", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "USER", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV", "SHELL"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 10, "parameter_count": 3, "complexity_score": 36, "parsing_method": "FALLBACK", "tables": ["DATE", "VBRK", "AND", "CT_BILLING_PAYME", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATI", "PAYMENT"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 4, "parameter_count": 3, "complexity_score": 11, "parsing_method": "FALLBACK", "tables": ["DATE", "BSEG", "AND", "CT_BSEG_DELTA_AC"], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 10, "parameter_count": 3, "complexity_score": 36, "parsing_method": "FALLBACK", "tables": ["DATE", "VBAK", "AND", "CT_ORDER_DELIVER", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATI", "STATISTICS"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "warnings": []}], "summary_report": {"summary": {"total_files": 4, "successful_extractions": 4, "failed_extractions": 0, "success_rate": 100.0, "unique_tables": 38, "total_parameters": 9, "average_complexity": 44.75}, "table_consolidation": {"KEY": {"procedures": [""], "procedure_count": 1}, "NVARCHAR": {"procedures": [""], "procedure_count": 1}, "DUMMY": {"procedures": [""], "procedure_count": 1}, "AND": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS", "PR_BSEG_ACTIVITY_LOAD", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 4}, "KNA1": {"procedures": [""], "procedure_count": 1}, "KNB1": {"procedures": [""], "procedure_count": 1}, "MARA": {"procedures": [""], "procedure_count": 1}, "VBAK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "LIKP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTK": {"procedures": [""], "procedure_count": 1}, "VBRK": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BKPF": {"procedures": [""], "procedure_count": 1}, "USER": {"procedures": [""], "procedure_count": 1}, "BSID": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSAD": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "CDHDR": {"procedures": [""], "procedure_count": 1}, "SALES": {"procedures": [""], "procedure_count": 1}, "BILLING": {"procedures": [""], "procedure_count": 1}, "KONV_DATA": {"procedures": [""], "procedure_count": 1}, "VBAP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUP": {"procedures": [""], "procedure_count": 1}, "LIPS": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTP": {"procedures": [""], "procedure_count": 1}, "VBRP": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSEG": {"procedures": ["", "PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 2}, "VBFA": {"procedures": [""], "procedure_count": 1}, "CDPOS": {"procedures": [""], "procedure_count": 1}, "KONV": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "SHELL": {"procedures": [""], "procedure_count": 1}, "DATE": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS", "PR_BSEG_ACTIVITY_LOAD", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 3}, "CT_BILLING_PAYME": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_PAYMENT_STATI": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "PAYMENT": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_BSEG_DELTA_AC": {"procedures": ["PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 1}, "CT_ORDER_DELIVER": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CT_PROCESS_STATI": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "STATISTICS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}}, "successful_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 30, "parameter_count": 0, "complexity_score": 96, "parsing_method": "FALLBACK", "tables": ["KEY", "NVARCHAR", "DUMMY", "AND", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "USER", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV", "SHELL"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 10, "parameter_count": 3, "complexity_score": 36, "parsing_method": "FALLBACK", "tables": ["DATE", "VBRK", "AND", "CT_BILLING_PAYME", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATI", "PAYMENT"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 4, "parameter_count": 3, "complexity_score": 11, "parsing_method": "FALLBACK", "tables": ["DATE", "BSEG", "AND", "CT_BSEG_DELTA_AC"], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 10, "parameter_count": 3, "complexity_score": 36, "parsing_method": "FALLBACK", "tables": ["DATE", "VBAK", "AND", "CT_ORDER_DELIVER", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATI", "STATISTICS"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "warnings": []}], "failed_results": []}, "timestamp": "1749718598.5678203"}