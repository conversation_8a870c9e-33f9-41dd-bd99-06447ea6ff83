"""
SAP HANA SQL Procedure Parser Package
====================================

This package provides tools for extracting detailed information from SAP HANA SQL procedures
using ANTLR parser with regex fallback support.

Main Components:
- HanaInformationExtractor: Main extraction class
- ProcedureInfo: Data model for extracted procedure information
- ExtractionResult: Result wrapper with success/error information

Usage:
    from parser import HanaInformationExtractor
    
    extractor = HanaInformationExtractor()
    result = extractor.extract_from_file('procedure.sql')
    
    if result.success:
        print(f"Procedure: {result.procedure_info.name}")
        print(f"Tables: {list(result.procedure_info.tables.keys())}")
"""

from .hana_extractor import HanaInformationExtractor
from .models import (
    ProcedureInfo, Parameter, Table, Column, FilterCondition, 
    JoinCondition, SQLStatement, Variable, ExtractionResult,
    ParameterDirection, JoinType
)

__version__ = "1.0.0"
__author__ = "SAP HANA Parser Team"

__all__ = [
    'HanaInformationExtractor',
    'ProcedureInfo',
    'Parameter', 
    'Table',
    'Column',
    'FilterCondition',
    'JoinCondition',
    'SQLStatement',
    'Variable',
    'ExtractionResult',
    'ParameterDirection',
    'JoinType'
]
