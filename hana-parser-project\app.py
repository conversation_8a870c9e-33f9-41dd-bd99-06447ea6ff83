"""
SAP HANA Procedure Information Extractor - Streamlit Web Application
===================================================================

A focused web application for extracting detailed information from SAP HANA SQL procedures
using ANTLR parser with regex fallback support.

Features:
- Upload and analyze SQL procedures
- Extract tables, parameters, joins, and filters
- Generate consolidated table analysis
- Export results to CSV
- Interactive data visualization
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import io
from datetime import datetime
from typing import List, Dict, Any, Optional
import sys
import os
from pathlib import Path

# Add the parent directory to access the generated ANTLR files
current_dir = Path(__file__).parent.parent
generated_dir = current_dir / "generated"
if generated_dir.exists():
    sys.path.insert(0, str(generated_dir))

# Import our parser modules
from parser import HanaInformationExtractor, ProcedureInfo, ExtractionResult

# Page configuration
st.set_page_config(
    page_title="SAP HANA Procedure Extractor",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

class HanaExtractorApp:
    """Main Streamlit application class for HANA procedure extraction"""
    
    def __init__(self):
        self.extractor = HanaInformationExtractor()
        
        # Initialize session state
        if 'extraction_results' not in st.session_state:
            st.session_state.extraction_results = []
        if 'consolidated_data' not in st.session_state:
            st.session_state.consolidated_data = None
    
    def run(self):
        """Main application entry point"""
        
        # Sidebar
        self._render_sidebar()
        
        # Main content
        st.title("🔍 SAP HANA Procedure Information Extractor")
        st.markdown("**Extract detailed information from SAP HANA SQL procedures using ANTLR parser**")
        
        # Main tabs
        tab1, tab2, tab3, tab4 = st.tabs([
            "📁 Upload & Extract", 
            "📊 Extraction Results", 
            "📋 Table Analysis",
            "📈 Visualizations"
        ])
        
        with tab1:
            self._render_upload_tab()
        
        with tab2:
            self._render_results_tab()
        
        with tab3:
            self._render_table_analysis_tab()
        
        with tab4:
            self._render_visualizations_tab()
    
    def _render_sidebar(self):
        """Render sidebar with configuration options"""
        
        st.sidebar.header("⚙️ Configuration")
        
        # Parser Information
        st.sidebar.subheader("Parser Information")
        parser_method = "ANTLR" if self.extractor.use_antlr else "Regex Fallback"
        st.sidebar.info(f"Using: {parser_method}")
        
        # Analysis Settings
        st.sidebar.subheader("Analysis Settings")
        max_procedures = st.sidebar.number_input(
            "Max Procedures to Process",
            min_value=1,
            max_value=100,
            value=50,
            help="Maximum number of procedures to process in one batch"
        )
        
        # Store settings in session state
        st.session_state.max_procedures = max_procedures
        
        # Download Section
        st.sidebar.subheader("📥 Downloads")
        if st.session_state.extraction_results:
            self._render_download_buttons()
        
        # Statistics
        if st.session_state.extraction_results:
            st.sidebar.subheader("📊 Statistics")
            total_procedures = len(st.session_state.extraction_results)
            successful = len([r for r in st.session_state.extraction_results if r['success']])
            st.sidebar.metric("Total Procedures", total_procedures)
            st.sidebar.metric("Successfully Parsed", successful)
            st.sidebar.metric("Success Rate", f"{(successful/total_procedures)*100:.1f}%")
    
    def _render_upload_tab(self):
        """Render the upload and extraction tab"""
        
        st.header("📁 Upload SQL Procedures")
        
        # File upload options
        upload_method = st.radio(
            "Choose upload method:",
            ["Upload Files", "Paste SQL Text"],
            horizontal=True
        )
        
        procedures_data = []
        
        if upload_method == "Upload Files":
            uploaded_files = st.file_uploader(
                "Upload SQL procedure files",
                type=['sql', 'hdbprocedure', 'txt'],
                accept_multiple_files=True,
                help="Upload your SAP HANA SQL procedure files"
            )
            
            if uploaded_files:
                st.success(f"📁 {len(uploaded_files)} files uploaded")
                
                for file in uploaded_files:
                    try:
                        content = file.read().decode('utf-8')
                        procedures_data.append({
                            'name': file.name,
                            'content': content,
                            'size': len(content)
                        })
                    except Exception as e:
                        st.error(f"Error reading {file.name}: {e}")
        
        else:  # Paste SQL Text
            st.subheader("Paste SQL Procedure")
            
            procedure_name = st.text_input(
                "Procedure Name",
                placeholder="e.g., PR_BILLING_PAYMENT_ANALYSIS"
            )
            
            sql_content = st.text_area(
                "SQL Procedure Content",
                height=300,
                placeholder="Paste your SAP HANA SQL procedure here..."
            )
            
            if procedure_name and sql_content:
                procedures_data.append({
                    'name': procedure_name,
                    'content': sql_content,
                    'size': len(sql_content)
                })
        
        # Extraction section
        if procedures_data:
            st.subheader("🔍 Information Extraction")
            
            # Show procedure summary
            total_size = sum(p['size'] for p in procedures_data)
            st.info(f"📊 Ready to extract from {len(procedures_data)} procedures ({total_size:,} characters)")
            
            # Limit check
            max_procs = st.session_state.get('max_procedures', 50)
            if len(procedures_data) > max_procs:
                st.warning(f"⚠️ Too many procedures. Limiting to first {max_procs} procedures.")
                procedures_data = procedures_data[:max_procs]
            
            # Extraction button
            if st.button("🚀 Start Extraction", type="primary"):
                self._run_extraction(procedures_data)
    
    def _run_extraction(self, procedures_data: List[Dict[str, Any]]):
        """Run the information extraction"""
        
        with st.spinner("🔍 Extracting information from SQL procedures..."):
            
            # Progress tracking
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # Extract information from procedures
                extraction_results = []
                
                for i, proc_data in enumerate(procedures_data):
                    status_text.text(f"Processing procedure {i+1}/{len(procedures_data)}: {proc_data['name']}")
                    
                    try:
                        result = self.extractor.extract_from_sql(proc_data['content'])
                        
                        extraction_results.append({
                            'name': proc_data['name'],
                            'success': result.success,
                            'procedure_info': result.procedure_info,
                            'errors': result.errors,
                            'warnings': result.warnings,
                            'parsing_method': result.parsing_method
                        })
                        
                    except Exception as e:
                        extraction_results.append({
                            'name': proc_data['name'],
                            'success': False,
                            'procedure_info': None,
                            'errors': [str(e)],
                            'warnings': [],
                            'parsing_method': 'ERROR'
                        })
                    
                    progress_bar.progress((i + 1) / len(procedures_data) * 0.9)
                
                # Store results
                st.session_state.extraction_results = extraction_results
                
                # Generate consolidated analysis
                status_text.text("Generating consolidated analysis...")
                self._generate_consolidated_analysis()
                
                progress_bar.progress(1.0)
                status_text.text("✅ Extraction complete!")
                
                successful = len([r for r in extraction_results if r['success']])
                st.success(f"🎉 Successfully extracted information from {successful}/{len(extraction_results)} procedures!")
                
                # Show quick summary
                self._show_quick_summary()
                
            except Exception as e:
                st.error(f"❌ Extraction failed: {e}")
                st.exception(e)

    def _generate_consolidated_analysis(self):
        """Generate consolidated analysis from extraction results"""

        successful_results = [r for r in st.session_state.extraction_results if r['success']]

        if not successful_results:
            return

        # Consolidate tables across all procedures
        table_consolidation = {}

        for result in successful_results:
            proc_info = result['procedure_info']
            proc_name = result['name']

            for table_name, table in proc_info.tables.items():
                if table_name not in table_consolidation:
                    table_consolidation[table_name] = {
                        'table_name': table_name,
                        'used_in_procedures': [],
                        'procedure_count': 0,
                        'all_columns': set(),
                        'usage_contexts': set(),
                        'total_references': 0
                    }

                table_data = table_consolidation[table_name]
                table_data['used_in_procedures'].append(proc_name)
                table_data['procedure_count'] += 1
                table_data['all_columns'].update(table.columns)
                table_data['usage_contexts'].update(table.usage_context)
                table_data['total_references'] += 1

        # Convert sets to lists for JSON serialization
        for table_name, table_data in table_consolidation.items():
            table_data['all_columns'] = list(table_data['all_columns'])
            table_data['usage_contexts'] = list(table_data['usage_contexts'])

        st.session_state.consolidated_data = {
            'table_consolidation': table_consolidation,
            'summary': {
                'total_procedures': len(successful_results),
                'unique_tables': len(table_consolidation),
                'timestamp': datetime.now().isoformat()
            }
        }

    def _show_quick_summary(self):
        """Show quick extraction summary"""

        if not st.session_state.consolidated_data:
            return

        summary = st.session_state.consolidated_data['summary']

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Procedures Processed", summary.get('total_procedures', 0))

        with col2:
            st.metric("Unique Tables Found", summary.get('unique_tables', 0))

        with col3:
            successful = len([r for r in st.session_state.extraction_results if r['success']])
            st.metric("Successfully Parsed", successful)

        with col4:
            total = len(st.session_state.extraction_results)
            success_rate = (successful / total * 100) if total > 0 else 0
            st.metric("Success Rate", f"{success_rate:.1f}%")

    def _render_results_tab(self):
        """Render the extraction results tab"""

        if not st.session_state.extraction_results:
            st.info("📊 No extraction results yet. Please upload and extract procedures first.")
            return

        st.header("📊 Extraction Results")

        # Summary metrics
        st.subheader("📈 Summary")
        self._show_quick_summary()

        # Individual procedure results
        st.subheader("📋 Individual Procedure Results")

        for result in st.session_state.extraction_results:
            with st.expander(f"{'✅' if result['success'] else '❌'} {result['name']}", expanded=False):

                if result['success']:
                    proc_info = result['procedure_info']

                    col1, col2 = st.columns(2)

                    with col1:
                        st.write("**Basic Information:**")
                        st.write(f"- **Name:** {proc_info.name}")
                        st.write(f"- **Language:** {proc_info.language}")
                        st.write(f"- **Parameters:** {proc_info.parameter_count}")
                        st.write(f"- **Tables:** {proc_info.table_count}")
                        st.write(f"- **Complexity Score:** {proc_info.complexity_score}")
                        st.write(f"- **Parsing Method:** {result['parsing_method']}")

                    with col2:
                        st.write("**Parameters:**")
                        if proc_info.parameters:
                            for param in proc_info.parameters:
                                st.write(f"- {param}")
                        else:
                            st.write("- No parameters found")

                        st.write("**Tables:**")
                        if proc_info.tables:
                            for table_name in proc_info.tables.keys():
                                st.write(f"- {table_name}")
                        else:
                            st.write("- No tables found")

                    # Show warnings if any
                    if result['warnings']:
                        st.warning("⚠️ Warnings:")
                        for warning in result['warnings']:
                            st.write(f"- {warning}")

                else:
                    st.error("❌ Extraction failed")
                    if result['errors']:
                        st.write("**Errors:**")
                        for error in result['errors']:
                            st.write(f"- {error}")

    def _render_table_analysis_tab(self):
        """Render the consolidated table analysis tab"""

        if not st.session_state.consolidated_data:
            st.info("📋 No consolidated data yet. Please extract procedure information first.")
            return

        st.header("📋 Consolidated Table Analysis")

        # Create table analysis dataframe
        table_df = self._create_table_analysis_dataframe()

        if not table_df.empty:
            st.subheader("📊 Table Usage Summary")

            # Display the dataframe
            st.dataframe(
                table_df,
                use_container_width=True,
                height=400
            )

            # Download button
            csv_data = table_df.to_csv(index=False)
            st.download_button(
                label="📥 Download Table Analysis (CSV)",
                data=csv_data,
                file_name=f"hana_table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

            # Table statistics
            st.subheader("📈 Table Statistics")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Total Tables", len(table_df))

            with col2:
                avg_procedures = table_df['Procedure Count'].mean() if not table_df.empty else 0
                st.metric("Avg Procedures per Table", f"{avg_procedures:.1f}")

            with col3:
                max_procedures = table_df['Procedure Count'].max() if not table_df.empty else 0
                st.metric("Max Procedures per Table", max_procedures)

        else:
            st.warning("No table data available for analysis.")

    def _create_table_analysis_dataframe(self) -> pd.DataFrame:
        """Create consolidated table analysis dataframe"""

        if not st.session_state.consolidated_data:
            return pd.DataFrame()

        table_consolidation = st.session_state.consolidated_data['table_consolidation']

        if not table_consolidation:
            return pd.DataFrame()

        rows = []

        for table_name, table_data in table_consolidation.items():
            # Format procedures using this table
            procedures_str = ", ".join(table_data['used_in_procedures'])

            # Format all columns used
            columns_str = ", ".join(sorted(table_data['all_columns']))

            # Format usage contexts
            usage_contexts_str = ", ".join(sorted(table_data['usage_contexts'])) if table_data['usage_contexts'] else "Unknown"

            row = {
                'Table Name': table_name,
                'Used in Procedures': procedures_str,
                'Procedure Count': table_data['procedure_count'],
                'Columns Used': columns_str,
                'Column Count': len(table_data['all_columns']),
                'Usage Contexts': usage_contexts_str,
                'Total References': table_data['total_references']
            }
            rows.append(row)

        # Create DataFrame and sort by procedure count
        df = pd.DataFrame(rows)
        if not df.empty:
            df = df.sort_values('Procedure Count', ascending=False)

        return df

    def _render_visualizations_tab(self):
        """Render the visualizations tab"""

        if not st.session_state.consolidated_data:
            st.info("📈 No data available for visualization. Please extract procedure information first.")
            return

        st.header("📈 Data Visualizations")

        table_consolidation = st.session_state.consolidated_data['table_consolidation']

        if not table_consolidation:
            st.warning("No table data available for visualization.")
            return

        # Table usage distribution
        st.subheader("📊 Table Usage Distribution")

        # Prepare data for visualization
        table_names = list(table_consolidation.keys())
        procedure_counts = [table_data['procedure_count'] for table_data in table_consolidation.values()]
        column_counts = [len(table_data['all_columns']) for table_data in table_consolidation.values()]

        # Bar chart of table usage
        fig_usage = px.bar(
            x=table_names,
            y=procedure_counts,
            title="Number of Procedures Using Each Table",
            labels={'x': 'Table Name', 'y': 'Number of Procedures'},
            color=procedure_counts,
            color_continuous_scale='viridis'
        )
        fig_usage.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_usage, use_container_width=True)

        # Column count distribution
        st.subheader("📊 Column Usage Distribution")

        fig_columns = px.bar(
            x=table_names,
            y=column_counts,
            title="Number of Columns Used per Table",
            labels={'x': 'Table Name', 'y': 'Number of Columns'},
            color=column_counts,
            color_continuous_scale='plasma'
        )
        fig_columns.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_columns, use_container_width=True)

        # Scatter plot: Procedures vs Columns
        st.subheader("📊 Procedures vs Columns Relationship")

        fig_scatter = px.scatter(
            x=procedure_counts,
            y=column_counts,
            hover_name=table_names,
            title="Relationship between Procedure Count and Column Count",
            labels={'x': 'Number of Procedures', 'y': 'Number of Columns'},
            size=[max(1, count) for count in procedure_counts],
            color=procedure_counts,
            color_continuous_scale='turbo'
        )
        st.plotly_chart(fig_scatter, use_container_width=True)

        # Top tables summary
        st.subheader("🏆 Top Tables by Usage")

        # Sort tables by procedure count
        sorted_tables = sorted(
            table_consolidation.items(),
            key=lambda x: x[1]['procedure_count'],
            reverse=True
        )

        top_n = min(10, len(sorted_tables))

        col1, col2 = st.columns(2)

        with col1:
            st.write("**Most Used Tables:**")
            for i, (table_name, table_data) in enumerate(sorted_tables[:top_n]):
                st.write(f"{i+1}. **{table_name}** - {table_data['procedure_count']} procedures")

        with col2:
            # Sort by column count
            sorted_by_columns = sorted(
                table_consolidation.items(),
                key=lambda x: len(x[1]['all_columns']),
                reverse=True
            )

            st.write("**Tables with Most Columns:**")
            for i, (table_name, table_data) in enumerate(sorted_by_columns[:top_n]):
                st.write(f"{i+1}. **{table_name}** - {len(table_data['all_columns'])} columns")

    def _render_download_buttons(self):
        """Render download buttons in sidebar"""

        if not st.session_state.extraction_results:
            return

        # Download extraction results as JSON
        results_json = json.dumps(
            st.session_state.extraction_results,
            default=str,
            indent=2
        )

        st.sidebar.download_button(
            label="📥 Download Results (JSON)",
            data=results_json,
            file_name=f"hana_extraction_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )

        # Download consolidated data as JSON
        if st.session_state.consolidated_data:
            consolidated_json = json.dumps(
                st.session_state.consolidated_data,
                default=str,
                indent=2
            )

            st.sidebar.download_button(
                label="📥 Download Consolidated Data (JSON)",
                data=consolidated_json,
                file_name=f"hana_consolidated_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

        # Download table analysis as CSV
        if st.session_state.consolidated_data:
            table_df = self._create_table_analysis_dataframe()
            if not table_df.empty:
                csv_data = table_df.to_csv(index=False)
                st.sidebar.download_button(
                    label="📥 Download Table Analysis (CSV)",
                    data=csv_data,
                    file_name=f"hana_table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )


# Main application entry point
def main():
    """Main application entry point"""
    app = HanaExtractorApp()
    app.run()


if __name__ == "__main__":
    main()
