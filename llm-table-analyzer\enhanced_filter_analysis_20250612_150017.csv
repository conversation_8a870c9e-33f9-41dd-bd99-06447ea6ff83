Table Name,Object Type,Schema,Used in Procedures,Procedure Count,All Columns,Usage Contexts,Filter Patterns,Recommended Filter Strategy,Filter Conflict Analysis,Join Relationships
SAP_SCHEMA.VBRK,TABLE,SAP_SCHEMA,"PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_ALL_BILLING.sql",3,"VBRK.VBELN, VBRK.FKDAT, VBRK.KUNAG, VBRK.NETWR, VBRK.FKART, VBRK.MANDT","FILTER, JOIN, SELECT","MANDT: [PR_BILLING: ='100', PR_INVOICE: ='200', PR_ALL_BILLING: ALL_VALUES] | FKDAT: [PR_BILLING,PR_INVOICE: BETWEEN dates, PR_ALL_BILLING: ALL_VALUES] | FKART: [ALL_PROCS: IN('F2','G2')]",USE_ALL_VALUES,"PR_BILLING,PR_INVOICE use MANDT filters, PR_ALL_BILLING needs all MANDT values - recommend ALL_VALUES for MANDT. FKDAT has similar conflict - recommend ALL_VALUES",→ VBRP: VBRK.MANDT = VBRP.MANDT AND VBRK.VBELN = VBRP.VBELN | → KNA1: VBRK.MANDT = KNA1.MANDT AND VBRK.KUNAG = KNA1.KUNNR
_SYS_BIC.BILLING_VIEW,CALCULATION_VIEW,_SYS_BIC,"PR_ANALYTICS_REPORT.sql, PR_DASHBOARD_DATA.sql",2,"BILLING_VIEW.CLIENT, BILLING_VIEW.BILLING_DATE, BILLING_VIEW.AMOUNT, BILLING_VIEW.CUSTOMER_ID","SELECT, FILTER","CLIENT: [PR_ANALYTICS: ='100', PR_DASHBOARD: ALL_VALUES] | BILLING_DATE: [ALL_PROCS: >= current_date - 365]",USE_ALL_VALUES,"PR_ANALYTICS restricts CLIENT='100', PR_DASHBOARD needs all clients - recommend ALL_VALUES for CLIENT",→ CUSTOMER_MASTER: BILLING_VIEW.CUSTOMER_ID = CUSTOMER_MASTER.ID
SAP_SCHEMA.VBAP,TABLE,SAP_SCHEMA,"PR_ORDER_ITEMS.sql, PR_MATERIAL_ANALYSIS.sql",2,"VBAP.VBELN, VBAP.POSNR, VBAP.MATNR, VBAP.KWMENG, VBAP.MANDT","FILTER, JOIN, SELECT","MANDT: [ALL_PROCS: ='100'] | MATNR: [PR_ORDER_ITEMS: LIKE 'MAT%', PR_MATERIAL: ALL_VALUES]",USE_ALL_VALUES,"PR_ORDER_ITEMS filters MATNR with LIKE 'MAT%', PR_MATERIAL needs all MATNR values - recommend ALL_VALUES for MATNR",→ VBAK: VBAP.MANDT = VBAK.MANDT AND VBAP.VBELN = VBAK.VBELN | → MARA: VBAP.MATNR = MARA.MATNR
CUSTOM_SCHEMA.ORDER_STATUS_VIEW,CDS_VIEW,CUSTOM_SCHEMA,PR_STATUS_REPORT.sql,1,"ORDER_STATUS_VIEW.ORDER_ID, ORDER_STATUS_VIEW.STATUS, ORDER_STATUS_VIEW.LAST_UPDATED","SELECT, FILTER","STATUS: [PR_STATUS_REPORT: IN('OPEN','PROCESSING')]",USE_RESTRICTIVE_FILTERS,Only one procedure uses this view with consistent STATUS filter - safe to use restrictive filters,→ VBAK: ORDER_STATUS_VIEW.ORDER_ID = VBAK.VBELN
SAP_SCHEMA.BSEG,TABLE,SAP_SCHEMA,"PR_ACCOUNTING_FULL.sql, PR_ACCOUNTING_DELTA.sql, PR_ACCOUNTING_SUMMARY.sql",3,"BSEG.BUKRS, BSEG.BELNR, BSEG.GJAHR, BSEG.BUZEI, BSEG.MANDT, BSEG.DMBTR","FILTER, JOIN, SELECT","MANDT: [PR_FULL,PR_SUMMARY: ALL_VALUES, PR_DELTA: ='100'] | GJAHR: [PR_FULL: ALL_VALUES, PR_DELTA,PR_SUMMARY: >=2023] | BUKRS: [ALL_PROCS: IN('1000','2000')]",USE_ALL_VALUES,"PR_FULL needs all MANDT and GJAHR values, others use filters - recommend ALL_VALUES for MANDT and GJAHR. BUKRS filter consistent across all procedures",→ BKPF: BSEG.MANDT = BKPF.MANDT AND BSEG.BUKRS = BKPF.BUKRS AND BSEG.BELNR = BKPF.BELNR AND BSEG.GJAHR = BKPF.GJAHR
