"""
Quick test of the LLM analyzer
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from llm_analyzer import GitHubModelsLLMAnalyzer, ProcedureLoader

# Test with a simple procedure
sample_sql = """
CREATE PROCEDURE PR_TEST_BILLING
(
    IN IP_DATE_FROM DATE,
    IN IP_DATE_TO DATE
)
LANGUAGE SQLSCRIPT
AS
BEGIN
    SELECT 
        V.VBELN,
        V.FKDAT,
        P.POSNR,
        P.MATNR
    FROM VBRK V
    INNER JOIN VBRP P ON V.VBELN = P.VBELN
    WHERE V.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO
        AND V.FKART = 'F2';
END;
"""

print("🤖 Testing LLM Analyzer...")

try:
    # Create analyzer
    analyzer = GitHubModelsLLMAnalyzer()
    
    # Test connection
    print("🔍 Testing connection...")
    if analyzer.test_connection():
        print("✅ Connection successful!")
        
        # Create procedure
        procedures = [ProcedureLoader.create_from_text("PR_TEST_BILLING", sample_sql)]
        
        # Analyze
        print("🔍 Analyzing procedure...")
        result = analyzer.analyze_procedures(procedures)
        
        if result.success:
            print("✅ Analysis successful!")
            print(f"📊 Found {result.total_tables} tables")
            
            if result.table_analysis:
                print("\n📋 Results:")
                for table in result.table_analysis:
                    print(f"  Table: {table.get('Table Name', 'Unknown')}")
                    print(f"  Columns: {table.get('All Columns', 'None')}")
                    print(f"  Filters: {table.get('Filter Patterns', 'None')}")
                    print(f"  Joins: {table.get('Join Relationships', 'None')}")
                    print()
        else:
            print(f"❌ Analysis failed: {result.error}")
    
    else:
        print("❌ Connection failed")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
