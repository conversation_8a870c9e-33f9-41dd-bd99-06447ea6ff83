"""
SAP HANA ANTLR Extraction Listener
=================================

Custom listener for extracting information from SAP HANA SQL procedures using ANTLR grammar.
"""

import sys
from pathlib import Path

# Add the generated ANTLR files to path
current_dir = Path(__file__).parent.parent.parent
generated_dir = current_dir / "generated"
if generated_dir.exists():
    sys.path.insert(0, str(generated_dir))

from HanaListener import HanaListener
from .models import (
    ProcedureInfo, Parameter, ParameterDirection, Table, 
    FilterCondition, JoinCondition, JoinType, Variable
)

class HanaExtractionListener(HanaListener):
    """Custom listener for extracting SAP HANA procedure information"""
    
    def __init__(self):
        self.procedure_info = ProcedureInfo(name="")
        self.errors = []
        self.warnings = []
        self.current_table_aliases = {}
        
    def enterProcedure_definition(self, ctx):
        """Extract procedure name and basic information"""
        try:
            # Extract procedure name
            if ctx.procedure_name():
                proc_name = ctx.procedure_name().getText()
                self.procedure_info.name = proc_name.strip('"').strip("'")
                
        except Exception as e:
            self.errors.append(f"Error extracting procedure definition: {e}")
    
    def enterParameter_declaration(self, ctx):
        """Extract procedure parameters"""
        try:
            # Extract parameter information
            param_name = ""
            param_type = ""
            direction = ParameterDirection.IN
            default_value = None
            
            # Get parameter name
            if ctx.parameter_name():
                param_name = ctx.parameter_name().getText()
            
            # Get parameter type
            if ctx.data_type():
                param_type = ctx.data_type().getText()
            
            # Get parameter direction
            if ctx.parameter_direction():
                direction_text = ctx.parameter_direction().getText().upper()
                if direction_text == 'OUT':
                    direction = ParameterDirection.OUT
                elif direction_text == 'INOUT':
                    direction = ParameterDirection.INOUT
            
            # Get default value
            if ctx.default_value():
                default_value = ctx.default_value().getText()
            
            if param_name and param_type:
                parameter = Parameter(
                    name=param_name,
                    data_type=param_type,
                    direction=direction,
                    default_value=default_value,
                    is_table_type='TABLE' in param_type.upper()
                )
                self.procedure_info.parameters.append(parameter)
                
        except Exception as e:
            self.errors.append(f"Error extracting parameter: {e}")
    
    def enterTable_reference(self, ctx):
        """Extract table references from FROM and JOIN clauses"""
        try:
            table_name = ""
            alias = None
            schema = None
            
            # Extract table name
            if ctx.table_name():
                full_name = ctx.table_name().getText()
                
                # Handle schema.table format
                if '.' in full_name:
                    parts = full_name.split('.')
                    schema = parts[0].strip('"').strip("'")
                    table_name = parts[1].strip('"').strip("'")
                else:
                    table_name = full_name.strip('"').strip("'")
            
            # Extract alias
            if ctx.table_alias():
                alias = ctx.table_alias().getText()
            
            if table_name:
                table_name = table_name.upper()
                
                # Create table object
                if table_name not in self.procedure_info.tables:
                    table = Table(
                        name=table_name,
                        alias=alias,
                        schema=schema
                    )
                    self.procedure_info.tables[table_name] = table
                
                # Store alias mapping
                if alias:
                    self.current_table_aliases[alias.upper()] = table_name
                    
        except Exception as e:
            self.errors.append(f"Error extracting table reference: {e}")
    
    def enterWhere_clause(self, ctx):
        """Extract WHERE clause conditions"""
        try:
            # Extract filter conditions from WHERE clause
            where_text = ctx.getText()
            self._extract_filter_conditions_from_text(where_text)
            
        except Exception as e:
            self.errors.append(f"Error extracting WHERE clause: {e}")
    
    def enterJoin_clause(self, ctx):
        """Extract JOIN conditions"""
        try:
            # Extract join information
            join_type = JoinType.INNER
            left_table = ""
            right_table = ""
            condition = ""
            
            # Get join type
            if ctx.join_type():
                join_type_text = ctx.join_type().getText().upper()
                if 'LEFT' in join_type_text:
                    join_type = JoinType.LEFT
                elif 'RIGHT' in join_type_text:
                    join_type = JoinType.RIGHT
                elif 'FULL' in join_type_text:
                    join_type = JoinType.FULL
                elif 'CROSS' in join_type_text:
                    join_type = JoinType.CROSS
            
            # Get join condition
            if ctx.join_condition():
                condition = ctx.join_condition().getText()
            
            # Extract table names from context
            # This is a simplified extraction - in practice, you'd need more sophisticated logic
            if condition:
                join_condition = JoinCondition(
                    left_table=left_table,
                    right_table=right_table,
                    join_type=join_type,
                    condition=condition,
                    left_columns=[],
                    right_columns=[]
                )
                self.procedure_info.all_joins.append(join_condition)
                
        except Exception as e:
            self.errors.append(f"Error extracting JOIN clause: {e}")
    
    def enterVariable_declaration(self, ctx):
        """Extract variable declarations"""
        try:
            var_name = ""
            var_type = ""
            is_constant = False
            default_value = None
            
            # Extract variable information
            if ctx.variable_name():
                var_name = ctx.variable_name().getText()
            
            if ctx.data_type():
                var_type = ctx.data_type().getText()
            
            if ctx.CONSTANT():
                is_constant = True
            
            if ctx.default_value():
                default_value = ctx.default_value().getText()
            
            if var_name and var_type:
                variable = Variable(
                    name=var_name,
                    data_type=var_type,
                    is_constant=is_constant,
                    default_value=default_value,
                    is_table_variable='TABLE' in var_type.upper()
                )
                self.procedure_info.variables.append(variable)
                
        except Exception as e:
            self.errors.append(f"Error extracting variable declaration: {e}")
    
    def _extract_filter_conditions_from_text(self, where_text: str):
        """Extract filter conditions from WHERE clause text"""
        # This is a simplified implementation
        # In practice, you'd use the parsed tree structure for more accurate extraction
        
        # For now, fall back to regex-based extraction
        # This could be enhanced to use the ANTLR parse tree
        pass
    
    def exitProcedure_definition(self, ctx):
        """Finalize procedure information extraction"""
        try:
            # Set final metadata
            self.procedure_info.table_count = len(self.procedure_info.tables)
            self.procedure_info.parameter_count = len(self.procedure_info.parameters)
            
            # Calculate complexity score
            complexity = 0
            complexity += len(self.procedure_info.tables) * 2
            complexity += len(self.procedure_info.parameters)
            complexity += len(self.procedure_info.all_filters)
            complexity += len(self.procedure_info.all_joins) * 2
            complexity += len(self.procedure_info.variables)
            
            self.procedure_info.complexity_score = complexity
            
        except Exception as e:
            self.errors.append(f"Error finalizing procedure information: {e}")
