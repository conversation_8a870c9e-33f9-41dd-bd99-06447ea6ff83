{"extraction_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 19, "parameter_count": 0, "complexity_score": 38, "parsing_method": "ANTLR", "tables": ["SHELL", "DD", "SELECTT2", "T2", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBAPAST2ONT1", "IP_DATE_TOANDT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUKAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUPAST2ONT1", "CURRENT_UTCTIMESTAMPFROMLIKPAST1INNERJOINLIPSAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVTTKAST1INNERJOINVTTPAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVBRKAST1INNERJOINVBRPAST2ONT1", "T1", "CURRENT_UTCTIMESTAMPFROMBKPFAST1INNERJOINBSEGAST2ONT1", "IP_BUDAT_TOANDT2", "IP_BUKRSANDT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBFAAST2ONT1", "CURRENT_UTCTIMESTAMPFROMCDHDRAST1INNERJOINCDPOSAST2ONT1", "ANDT1", "IP_VKORGUNIONALLSELECTT2"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 24, "parameter_count": 3, "complexity_score": 51, "parsing_method": "ANTLR", "tables": ["SELECTVBRK", "VBRK", "VBRP", "BSAD", "CASEWHENBSAD", "CASEWHENKONV", "THENKONV", "CURRENT_TIMESTAMPASCREATED_ATFROMVBRKINNERJOINVBRPONVBRK", "BSID", "KONV", "IP_FKDAT_TOANDVBRK", "IP_BUKRSANDVBRK", "GROUPBYVBRK", "WHENBSAD", "WHENKONV", "FROMVBRKINNERJOINVBRPONVBRK", "VBRKINNERJOINVBRPONVBRK", "JOINVBRPONVBRK", "ONVBRK", "LEFTJOINBSIDONVBRK", "LEFTJOINBSADONBSID", "ONBSID", "LEFTJOINKONVONVBRK", "WHEREVBRK"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4)"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 0, "parameter_count": 3, "complexity_score": 3, "parsing_method": "ANTLR", "tables": [], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4)"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 24, "parameter_count": 3, "complexity_score": 51, "parsing_method": "ANTLR", "tables": ["SELECTVBAK", "VBAK", "LIKP", "VBAP", "LIPS", "CASEWHENVBUK", "WHENVBUK", "CASEWHENLIKP", "WHENLIKP", "CURRENT_TIMESTAMPASCREATED_ATFROMVBAKINNERJOINVBAPONVBAK", "VBUK", "IP_ERDAT_TOANDVBAK", "IP_VKORGANDVBAK", "GROUPBYVBAK", "FROMVBAKINNERJOINVBAPONVBAK", "VBAKINNERJOINVBAPONVBAK", "JOINVBAPONVBAK", "ONVBAK", "INNERJOINVBUKONVBAK", "LEFTJOINLIPSONVBAP", "ONVBAP", "LEFTJOINLIKPONLIPS", "ONLIPS", "WHEREVBAK"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4)"], "warnings": []}], "summary_report": {"summary": {"total_files": 4, "successful_extractions": 4, "failed_extractions": 0, "success_rate": 100.0, "unique_tables": 67, "total_parameters": 9, "average_complexity": 35.75}, "table_consolidation": {"SHELL": {"procedures": [""], "procedure_count": 1}, "DD": {"procedures": [""], "procedure_count": 1}, "SELECTT2": {"procedures": [""], "procedure_count": 1}, "T2": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBAPAST2ONT1": {"procedures": [""], "procedure_count": 1}, "IP_DATE_TOANDT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUKAST2ONT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUPAST2ONT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMLIKPAST1INNERJOINLIPSAST2ONT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVTTKAST1INNERJOINVTTPAST2ONT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVBRKAST1INNERJOINVBRPAST2ONT1": {"procedures": [""], "procedure_count": 1}, "T1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMBKPFAST1INNERJOINBSEGAST2ONT1": {"procedures": [""], "procedure_count": 1}, "IP_BUDAT_TOANDT2": {"procedures": [""], "procedure_count": 1}, "IP_BUKRSANDT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBFAAST2ONT1": {"procedures": [""], "procedure_count": 1}, "CURRENT_UTCTIMESTAMPFROMCDHDRAST1INNERJOINCDPOSAST2ONT1": {"procedures": [""], "procedure_count": 1}, "ANDT1": {"procedures": [""], "procedure_count": 1}, "IP_VKORGUNIONALLSELECTT2": {"procedures": [""], "procedure_count": 1}, "SELECTVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "VBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "VBRP": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "BSAD": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CASEWHENBSAD": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CASEWHENKONV": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "THENKONV": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CURRENT_TIMESTAMPASCREATED_ATFROMVBRKINNERJOINVBRPONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "BSID": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "KONV": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "IP_FKDAT_TOANDVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "IP_BUKRSANDVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "GROUPBYVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "WHENBSAD": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "WHENKONV": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "FROMVBRKINNERJOINVBRPONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "VBRKINNERJOINVBRPONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "JOINVBRPONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "ONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "LEFTJOINBSIDONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "LEFTJOINBSADONBSID": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "ONBSID": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "LEFTJOINKONVONVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "WHEREVBRK": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "SELECTVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "VBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "LIKP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "VBAP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "LIPS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CASEWHENVBUK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "WHENVBUK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CASEWHENLIKP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "WHENLIKP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CURRENT_TIMESTAMPASCREATED_ATFROMVBAKINNERJOINVBAPONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "VBUK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "IP_ERDAT_TOANDVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "IP_VKORGANDVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "GROUPBYVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "FROMVBAKINNERJOINVBAPONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "VBAKINNERJOINVBAPONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "JOINVBAPONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "ONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "INNERJOINVBUKONVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "LEFTJOINLIPSONVBAP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "ONVBAP": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "LEFTJOINLIKPONLIPS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "ONLIPS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "WHEREVBAK": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}}, "successful_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 19, "parameter_count": 0, "complexity_score": 38, "parsing_method": "ANTLR", "tables": ["SHELL", "DD", "SELECTT2", "T2", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBAPAST2ONT1", "IP_DATE_TOANDT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUKAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBUPAST2ONT1", "CURRENT_UTCTIMESTAMPFROMLIKPAST1INNERJOINLIPSAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVTTKAST1INNERJOINVTTPAST2ONT1", "CURRENT_UTCTIMESTAMPFROMVBRKAST1INNERJOINVBRPAST2ONT1", "T1", "CURRENT_UTCTIMESTAMPFROMBKPFAST1INNERJOINBSEGAST2ONT1", "IP_BUDAT_TOANDT2", "IP_BUKRSANDT1", "CURRENT_UTCTIMESTAMPFROMVBAKAST1INNERJOINVBFAAST2ONT1", "CURRENT_UTCTIMESTAMPFROMCDHDRAST1INNERJOINCDPOSAST2ONT1", "ANDT1", "IP_VKORGUNIONALLSELECTT2"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 24, "parameter_count": 3, "complexity_score": 51, "parsing_method": "ANTLR", "tables": ["SELECTVBRK", "VBRK", "VBRP", "BSAD", "CASEWHENBSAD", "CASEWHENKONV", "THENKONV", "CURRENT_TIMESTAMPASCREATED_ATFROMVBRKINNERJOINVBRPONVBRK", "BSID", "KONV", "IP_FKDAT_TOANDVBRK", "IP_BUKRSANDVBRK", "GROUPBYVBRK", "WHENBSAD", "WHENKONV", "FROMVBRKINNERJOINVBRPONVBRK", "VBRKINNERJOINVBRPONVBRK", "JOINVBRPONVBRK", "ONVBRK", "LEFTJOINBSIDONVBRK", "LEFTJOINBSADONBSID", "ONBSID", "LEFTJOINKONVONVBRK", "WHEREVBRK"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4)"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 0, "parameter_count": 3, "complexity_score": 3, "parsing_method": "ANTLR", "tables": [], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4)"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 24, "parameter_count": 3, "complexity_score": 51, "parsing_method": "ANTLR", "tables": ["SELECTVBAK", "VBAK", "LIKP", "VBAP", "LIPS", "CASEWHENVBUK", "WHENVBUK", "CASEWHENLIKP", "WHENLIKP", "CURRENT_TIMESTAMPASCREATED_ATFROMVBAKINNERJOINVBAPONVBAK", "VBUK", "IP_ERDAT_TOANDVBAK", "IP_VKORGANDVBAK", "GROUPBYVBAK", "FROMVBAKINNERJOINVBAPONVBAK", "VBAKINNERJOINVBAPONVBAK", "JOINVBAPONVBAK", "ONVBAK", "INNERJOINVBUKONVBAK", "LEFTJOINLIPSONVBAP", "ONVBAP", "LEFTJOINLIKPONLIPS", "ONLIPS", "WHEREVBAK"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4)"], "warnings": []}], "failed_results": []}, "timestamp": "1749718598.5678203"}