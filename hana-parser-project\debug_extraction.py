"""
Debug the table extraction to see where AND and DATE are coming from
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from parser import HanaInformationExtractor

# Test with a simple SQL that has AND in JOIN condition
test_sql = """
CREATE PROCEDURE TEST_PROC()
AS
BEGIN
    SELECT V.VBELN, P.POSNR
    FROM VBAK V
    INNER JOIN VBAP P ON V.VBELN = P.VBELN
        AND V.MANDT = P.MANDT
    WHERE V.ERDAT >= '2024-01-01';
END;
"""

print("🔍 Debugging table extraction...")
print("SQL:", test_sql)

extractor = HanaInformationExtractor()
result = extractor.extract_from_sql(test_sql)

if result.success:
    proc_info = result.procedure_info
    print(f"\n📋 Tables found: {list(proc_info.tables.keys())}")
    
    for table_name, table in proc_info.tables.items():
        print(f"  {table_name}: {list(table.columns)} (usage: {list(table.usage_context)})")
else:
    print("❌ Extraction failed")
    for error in result.errors:
        print(f"Error: {error}")
