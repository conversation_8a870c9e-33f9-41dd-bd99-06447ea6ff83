"""
Basic Tests for SAP HANA Procedure Information Extractor
========================================================

Simple tests to verify the extraction functionality works correctly.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path to import our modules
current_dir = Path(__file__).parent
project_dir = current_dir.parent
sys.path.insert(0, str(project_dir))

from parser import HanaInformationExtractor, ProcedureInfo, ExtractionResult, ParameterDirection


def test_basic_extraction():
    """Test basic extraction functionality"""
    
    print("🧪 Testing basic extraction functionality...")
    
    # Sample SQL procedure
    sample_sql = """
    CREATE PROCEDURE PR_TEST_PROCEDURE
    (
        IN IP_DATE_FROM DATE,
        IN IP_DATE_TO DATE,
        OUT OP_RESULT_COUNT INTEGER
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        DECLARE lv_count INTEGER;
        
        SELECT COUNT(*)
        INTO lv_count
        FROM VBAK V
        INNER JOIN VBAP P ON V.VBELN = P.VBELN
        WHERE V.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO;
        
        OP_RESULT_COUNT := lv_count;
    END;
    """
    
    # Initialize extractor
    extractor = HanaInformationExtractor()
    
    # Extract information
    result = extractor.extract_from_sql(sample_sql)
    
    # Verify basic success
    assert result is not None, "Result should not be None"
    print(f"✅ Extraction completed with method: {result.parsing_method}")
    
    if result.success:
        proc_info = result.procedure_info
        
        # Test procedure name
        assert proc_info.name == "PR_TEST_PROCEDURE", f"Expected 'PR_TEST_PROCEDURE', got '{proc_info.name}'"
        print(f"✅ Procedure name: {proc_info.name}")
        
        # Test language
        assert proc_info.language == "SQLSCRIPT", f"Expected 'SQLSCRIPT', got '{proc_info.language}'"
        print(f"✅ Language: {proc_info.language}")
        
        # Test parameters
        assert proc_info.parameter_count >= 2, f"Expected at least 2 parameters, got {proc_info.parameter_count}"
        print(f"✅ Parameters: {proc_info.parameter_count}")
        
        # Test tables
        assert proc_info.table_count >= 1, f"Expected at least 1 table, got {proc_info.table_count}"
        print(f"✅ Tables: {proc_info.table_count}")
        
        # Test specific tables
        table_names = list(proc_info.tables.keys())
        print(f"✅ Tables found: {table_names}")
        
        # Test parameters details
        print("✅ Parameters details:")
        for param in proc_info.parameters:
            print(f"   - {param}")
        
        # Test complexity score
        assert proc_info.complexity_score > 0, f"Expected complexity score > 0, got {proc_info.complexity_score}"
        print(f"✅ Complexity score: {proc_info.complexity_score}")
        
        print("✅ All basic tests passed!")
        return True
    
    else:
        print("❌ Extraction failed:")
        for error in result.errors:
            print(f"   Error: {error}")
        return False


def test_parameter_parsing():
    """Test parameter parsing specifically"""
    
    print("\n🧪 Testing parameter parsing...")
    
    sample_sql = """
    CREATE PROCEDURE PR_PARAM_TEST
    (
        IN IP_STRING VARCHAR(100),
        OUT OP_COUNT INTEGER,
        INOUT IO_FLAG CHAR(1)
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        -- Simple procedure body
        OP_COUNT := 1;
    END;
    """
    
    extractor = HanaInformationExtractor()
    result = extractor.extract_from_sql(sample_sql)
    
    if result.success:
        proc_info = result.procedure_info
        
        # Check parameter count
        assert proc_info.parameter_count >= 2, f"Expected at least 2 parameters, got {proc_info.parameter_count}"
        
        # Check parameter directions (if parsed correctly)
        input_params = proc_info.get_input_parameters()
        output_params = proc_info.get_output_parameters()
        
        print(f"✅ Input parameters: {len(input_params)}")
        print(f"✅ Output parameters: {len(output_params)}")
        
        for param in proc_info.parameters:
            print(f"   - {param}")
        
        print("✅ Parameter parsing test passed!")
        return True
    
    else:
        print("❌ Parameter parsing test failed:")
        for error in result.errors:
            print(f"   Error: {error}")
        return False


def test_table_extraction():
    """Test table extraction specifically"""
    
    print("\n🧪 Testing table extraction...")
    
    sample_sql = """
    CREATE PROCEDURE PR_TABLE_TEST
    (
        IN IP_CLIENT VARCHAR(3)
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        INSERT INTO CT_RESULT
        SELECT 
            V.VBELN,
            V.ERDAT,
            P.POSNR,
            P.MATNR
        FROM VBAK V
        INNER JOIN VBAP P ON V.VBELN = P.VBELN
        LEFT JOIN MARA M ON P.MATNR = M.MATNR
        WHERE V.MANDT = :IP_CLIENT;
    END;
    """
    
    extractor = HanaInformationExtractor()
    result = extractor.extract_from_sql(sample_sql)
    
    if result.success:
        proc_info = result.procedure_info
        
        # Check table count
        assert proc_info.table_count >= 3, f"Expected at least 3 tables, got {proc_info.table_count}"
        
        table_names = list(proc_info.tables.keys())
        print(f"✅ Tables found: {table_names}")
        
        # Check for specific tables
        expected_tables = ['VBAK', 'VBAP', 'MARA', 'CT_RESULT']
        found_tables = [t for t in expected_tables if t in table_names]
        
        print(f"✅ Expected tables found: {found_tables}")
        
        # Check columns
        for table_name, table in proc_info.tables.items():
            if table.columns:
                print(f"✅ {table_name} columns: {list(table.columns)}")
        
        print("✅ Table extraction test passed!")
        return True
    
    else:
        print("❌ Table extraction test failed:")
        for error in result.errors:
            print(f"   Error: {error}")
        return False


def test_error_handling():
    """Test error handling with invalid SQL"""
    
    print("\n🧪 Testing error handling...")
    
    # Invalid SQL
    invalid_sql = "This is not valid SQL at all!"
    
    extractor = HanaInformationExtractor()
    result = extractor.extract_from_sql(invalid_sql)
    
    # Should not crash, but may not succeed
    assert result is not None, "Result should not be None even for invalid SQL"
    
    print(f"✅ Error handling test completed")
    print(f"   Success: {result.success}")
    print(f"   Parsing method: {result.parsing_method}")
    
    if result.errors:
        print(f"   Errors captured: {len(result.errors)}")
    
    return True


def run_all_tests():
    """Run all tests"""
    
    print("🧪 Running SAP HANA Procedure Extractor Tests")
    print("=" * 60)
    
    tests = [
        test_basic_extraction,
        test_parameter_parsing,
        test_table_extraction,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️ {total - passed} tests failed")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
