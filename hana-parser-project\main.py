"""
SAP HANA Procedure Information Extractor - Main Entry Point
===========================================================

This script provides a command-line interface for extracting information
from SAP HANA SQL procedures using ANTLR parser.

Usage:
    python main.py --file procedure.sql
    python main.py --directory /path/to/procedures/
    python main.py --streamlit  # Launch Streamlit web app
"""

import argparse
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
import json

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from parser import HanaInformationExtractor, ProcedureInfo, ExtractionResult


def extract_from_file(file_path: str, extractor: HanaInformationExtractor) -> Dict[str, Any]:
    """Extract information from a single file"""
    
    print(f"📁 Processing: {file_path}")
    
    try:
        result = extractor.extract_from_file(file_path)
        
        if result.success:
            proc_info = result.procedure_info
            print(f"✅ Success - {proc_info.name}")
            print(f"   Tables: {proc_info.table_count}, Parameters: {proc_info.parameter_count}")
            print(f"   Complexity: {proc_info.complexity_score}, Method: {result.parsing_method}")
            
            return {
                'file': file_path,
                'success': True,
                'procedure_name': proc_info.name,
                'table_count': proc_info.table_count,
                'parameter_count': proc_info.parameter_count,
                'complexity_score': proc_info.complexity_score,
                'parsing_method': result.parsing_method,
                'tables': list(proc_info.tables.keys()),
                'parameters': [str(p) for p in proc_info.parameters],
                'warnings': result.warnings
            }
        else:
            print(f"❌ Failed")
            for error in result.errors:
                print(f"   Error: {error}")
            
            return {
                'file': file_path,
                'success': False,
                'errors': result.errors
            }
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {
            'file': file_path,
            'success': False,
            'errors': [str(e)]
        }


def extract_from_directory(directory_path: str, extractor: HanaInformationExtractor) -> List[Dict[str, Any]]:
    """Extract information from all SQL files in a directory"""
    
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"❌ Directory not found: {directory_path}")
        return []
    
    # Find all SQL files
    sql_files = []
    for ext in ['*.sql', '*.hdbprocedure', '*.txt']:
        sql_files.extend(directory.glob(ext))
    
    if not sql_files:
        print(f"❌ No SQL files found in: {directory_path}")
        return []
    
    print(f"🔍 Found {len(sql_files)} SQL files")
    print("=" * 50)
    
    results = []
    
    for sql_file in sql_files:
        result = extract_from_file(str(sql_file), extractor)
        results.append(result)
        print()  # Empty line for readability
    
    return results


def generate_summary_report(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate a summary report from extraction results"""
    
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    # Consolidate tables
    table_consolidation = {}
    
    for result in successful_results:
        proc_name = result['procedure_name']
        
        for table_name in result['tables']:
            if table_name not in table_consolidation:
                table_consolidation[table_name] = {
                    'procedures': [],
                    'procedure_count': 0
                }
            
            table_consolidation[table_name]['procedures'].append(proc_name)
            table_consolidation[table_name]['procedure_count'] += 1
    
    # Calculate statistics
    total_procedures = len(results)
    successful_procedures = len(successful_results)
    success_rate = (successful_procedures / total_procedures * 100) if total_procedures > 0 else 0
    
    unique_tables = len(table_consolidation)
    total_parameters = sum(r.get('parameter_count', 0) for r in successful_results)
    avg_complexity = sum(r.get('complexity_score', 0) for r in successful_results) / len(successful_results) if successful_results else 0
    
    return {
        'summary': {
            'total_files': total_procedures,
            'successful_extractions': successful_procedures,
            'failed_extractions': len(failed_results),
            'success_rate': success_rate,
            'unique_tables': unique_tables,
            'total_parameters': total_parameters,
            'average_complexity': avg_complexity
        },
        'table_consolidation': table_consolidation,
        'successful_results': successful_results,
        'failed_results': failed_results
    }


def print_summary_report(report: Dict[str, Any]):
    """Print a formatted summary report"""
    
    summary = report['summary']
    
    print("=" * 60)
    print("📊 EXTRACTION SUMMARY REPORT")
    print("=" * 60)
    
    print(f"Total files processed: {summary['total_files']}")
    print(f"Successful extractions: {summary['successful_extractions']}")
    print(f"Failed extractions: {summary['failed_extractions']}")
    print(f"Success rate: {summary['success_rate']:.1f}%")
    print(f"Unique tables found: {summary['unique_tables']}")
    print(f"Total parameters: {summary['total_parameters']}")
    print(f"Average complexity score: {summary['average_complexity']:.1f}")
    
    # Top tables
    print(f"\n📋 TOP TABLES BY USAGE")
    print("-" * 30)
    
    table_consolidation = report['table_consolidation']
    sorted_tables = sorted(
        table_consolidation.items(),
        key=lambda x: x[1]['procedure_count'],
        reverse=True
    )
    
    for i, (table_name, table_data) in enumerate(sorted_tables[:10]):
        print(f"{i+1:2d}. {table_name} - {table_data['procedure_count']} procedures")
    
    # Failed extractions
    failed_results = report['failed_results']
    if failed_results:
        print(f"\n❌ FAILED EXTRACTIONS")
        print("-" * 30)
        for result in failed_results:
            print(f"- {Path(result['file']).name}")
            for error in result.get('errors', []):
                print(f"  Error: {error}")


def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(
        description="SAP HANA Procedure Information Extractor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --file procedure.sql
  python main.py --directory ../sample_procedures/
  python main.py --streamlit
  python main.py --file procedure.sql --output results.json
        """
    )
    
    parser.add_argument('--file', '-f', help='Extract from a single SQL file')
    parser.add_argument('--directory', '-d', help='Extract from all SQL files in a directory')
    parser.add_argument('--streamlit', '-s', action='store_true', help='Launch Streamlit web application')
    parser.add_argument('--output', '-o', help='Output file for results (JSON format)')
    
    args = parser.parse_args()
    
    # Check if no arguments provided
    if not any([args.file, args.directory, args.streamlit]):
        parser.print_help()
        return
    
    # Launch Streamlit app
    if args.streamlit:
        print("🚀 Launching Streamlit web application...")
        os.system("streamlit run app.py")
        return
    
    # Initialize extractor
    print("🔧 Initializing SAP HANA Information Extractor...")
    extractor = HanaInformationExtractor()
    
    results = []
    
    # Process single file
    if args.file:
        result = extract_from_file(args.file, extractor)
        results = [result]
    
    # Process directory
    elif args.directory:
        results = extract_from_directory(args.directory, extractor)
    
    # Generate and display summary
    if results:
        report = generate_summary_report(results)
        print_summary_report(report)
        
        # Save results to file if requested
        if args.output:
            output_data = {
                'extraction_results': results,
                'summary_report': report,
                'timestamp': str(Path(__file__).stat().st_mtime)
            }
            
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, default=str)
            
            print(f"\n💾 Results saved to: {args.output}")


if __name__ == "__main__":
    main()
