# SAP HANA Procedure Information Extractor

A focused tool for extracting detailed information from SAP HANA SQL procedures using ANTLR parser with regex fallback support.

## 🚀 Features

- **ANTLR-based Parsing**: Uses the SAP HANA grammar for accurate parsing
- **Regex Fallback**: Automatic fallback to regex parsing when ANTLR fails
- **Comprehensive Extraction**: Extracts tables, parameters, joins, filters, and more
- **Streamlit Web Interface**: User-friendly web application
- **Command Line Interface**: Batch processing capabilities
- **Export Capabilities**: CSV and JSON export options
- **Consolidated Analysis**: Cross-procedure table analysis

## 📋 What Information is Extracted

### Procedure Information
- Procedure name and language
- Security mode and default schema
- Parameters (IN/OUT/INOUT with data types)
- Declared variables
- Complexity score

### Table Analysis
- Table names and aliases
- Column references
- Usage contexts (SELECT, INSERT, UPDATE, DELETE)
- Cross-procedure consolidation

### SQL Patterns
- JOIN relationships and conditions
- WHERE clause filters
- Parameter usage

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- ANTLR4 runtime for Python

### Setup

1. **Clone or download the project**
   ```bash
   cd hana-parser-project
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Ensure ANTLR generated files are available**
   - The parser expects ANTLR generated files in `../generated/` directory
   - These should include: `HanaLexer.py`, `HanaParser.py`, `HanaListener.py`

## 🖥️ Usage

### Streamlit Web Application

Launch the web interface:
```bash
streamlit run app.py
```

Or using the main script:
```bash
python main.py --streamlit
```

#### Web Interface Features:
- **Upload & Extract**: Upload SQL files or paste SQL text
- **Extraction Results**: View detailed results for each procedure
- **Table Analysis**: Consolidated table usage analysis with CSV export
- **Visualizations**: Interactive charts and graphs

### Command Line Interface

#### Extract from a single file:
```bash
python main.py --file procedure.sql
```

#### Extract from all files in a directory:
```bash
python main.py --directory /path/to/procedures/
```

#### Save results to JSON file:
```bash
python main.py --directory /path/to/procedures/ --output results.json
```

### Python API

```python
from parser import HanaInformationExtractor

# Initialize extractor
extractor = HanaInformationExtractor()

# Extract from file
result = extractor.extract_from_file('procedure.sql')

if result.success:
    proc_info = result.procedure_info
    print(f"Procedure: {proc_info.name}")
    print(f"Tables: {list(proc_info.tables.keys())}")
    print(f"Parameters: {len(proc_info.parameters)}")
else:
    print("Extraction failed:", result.errors)
```

## 📊 Output Format

### Table Analysis CSV
The consolidated table analysis provides one row per table with:

| Column | Description |
|--------|-------------|
| Table Name | Name of the table |
| Used in Procedures | List of procedures using this table |
| Procedure Count | Number of procedures using this table |
| Columns Used | All columns referenced across procedures |
| Column Count | Number of unique columns used |
| Usage Contexts | How the table is used (SELECT, INSERT, etc.) |
| Total References | Total number of references |

### JSON Output Structure
```json
{
  "extraction_results": [
    {
      "file": "procedure.sql",
      "success": true,
      "procedure_name": "PR_SAMPLE",
      "table_count": 3,
      "parameter_count": 2,
      "complexity_score": 15,
      "parsing_method": "ANTLR",
      "tables": ["VBAK", "VBAP", "BSEG"],
      "parameters": ["IN IP_DATE DATE", "OUT OP_COUNT INTEGER"]
    }
  ],
  "summary_report": {
    "summary": {
      "total_files": 5,
      "successful_extractions": 4,
      "success_rate": 80.0,
      "unique_tables": 12
    },
    "table_consolidation": {
      "VBAK": {
        "procedures": ["PR_SAMPLE", "PR_ANALYSIS"],
        "procedure_count": 2
      }
    }
  }
}
```

## 🏗️ Architecture

### Core Components

1. **HanaInformationExtractor**: Main extraction engine
2. **HanaExtractionListener**: ANTLR listener for parsing
3. **Data Models**: Structured representation of extracted information
4. **Streamlit App**: Web interface
5. **CLI Interface**: Command-line tools

### Parsing Strategy

1. **Primary**: ANTLR-based parsing using SAP HANA grammar
2. **Fallback**: Regex-based parsing for robustness
3. **Post-processing**: Data consolidation and analysis

## 📁 Project Structure

```
hana-parser-project/
├── parser/
│   ├── __init__.py
│   ├── models.py          # Data models
│   └── hana_extractor.py  # Main extraction logic
├── examples/
│   └── extract_procedure_info.py
├── tests/
├── app.py                 # Streamlit web application
├── main.py               # Command-line interface
├── requirements.txt
└── README.md
```

## 🔧 Configuration

The extractor automatically detects ANTLR availability and falls back to regex parsing if needed. No additional configuration is required.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is provided as-is for educational and development purposes.

## 🐛 Troubleshooting

### Common Issues

1. **ANTLR files not found**
   - Ensure the `../generated/` directory contains ANTLR generated files
   - The extractor will fall back to regex parsing automatically

2. **Import errors**
   - Check that all dependencies are installed: `pip install -r requirements.txt`
   - Ensure Python path includes the project directory

3. **Parsing failures**
   - Check the SQL syntax is valid SAP HANA SQL
   - Review error messages in the extraction results
   - Try with simpler procedures first

### Getting Help

- Check the examples in the `examples/` directory
- Review the extraction results for error messages
- Use the Streamlit interface for interactive debugging

## 📈 Performance

- **ANTLR parsing**: More accurate but slower
- **Regex fallback**: Faster but less comprehensive
- **Batch processing**: Optimized for multiple files
- **Memory usage**: Efficient for large procedures

## 🎯 Use Cases

- **Migration Planning**: Analyze table dependencies
- **Code Documentation**: Generate procedure documentation
- **Impact Analysis**: Understand cross-procedure relationships
- **Quality Assessment**: Identify complex procedures
- **Data Lineage**: Track table usage patterns
