"""
Debug the filter extraction to see why filters are not being detected
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from parser import HanaInformationExtractor

# Test with a simple SQL that has WHERE conditions
test_sql = """
CREATE PROCEDURE TEST_FILTER_PROC()
AS
BEGIN
    SELECT V.VBELN, P.POSNR
    FROM VBAK V
    INNER JOIN VBAP P ON V.VBELN = P.VBELN
    WHERE V.ERDAT >= '2024-01-01'
        AND V.MANDT = '100'
        AND P.MATNR IN ('MAT001', 'MAT002')
        AND V.VKORG LIKE 'US%';
END;
"""

print("🔍 Debugging filter extraction...")
print("SQL:", test_sql)

extractor = HanaInformationExtractor()
result = extractor.extract_from_sql(test_sql)

if result.success:
    proc_info = result.procedure_info
    print(f"\n📋 Tables found: {list(proc_info.tables.keys())}")
    print(f"🔍 Filters found: {len(proc_info.all_filters)}")
    
    if proc_info.all_filters:
        print("\n📊 Filter Details:")
        for i, filter_cond in enumerate(proc_info.all_filters, 1):
            print(f"  {i}. {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
            print(f"     Condition: {filter_cond.condition_text}")
            print(f"     Is Parameter: {filter_cond.is_parameter}")
    else:
        print("❌ No filters detected!")
        
    print(f"\n🔗 Joins found: {len(proc_info.all_joins)}")
    if proc_info.all_joins:
        print("\n📊 Join Details:")
        for i, join_cond in enumerate(proc_info.all_joins, 1):
            print(f"  {i}. {join_cond.left_table} {join_cond.join_type.value} {join_cond.right_table}")
            print(f"     Condition: {join_cond.condition}")
    
else:
    print("❌ Extraction failed")
    for error in result.errors:
        print(f"Error: {error}")
