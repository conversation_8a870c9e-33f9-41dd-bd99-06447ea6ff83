"""
Example: Extract Information from SAP HANA Procedures
====================================================

This example demonstrates how to use the HanaInformationExtractor
to extract detailed information from SAP HANA SQL procedures.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path to import our modules
current_dir = Path(__file__).parent
project_dir = current_dir.parent
sys.path.insert(0, str(project_dir))

from parser import HanaInformationExtractor, ProcedureInfo, ExtractionResult


def extract_from_sample_procedures():
    """Extract information from sample procedures"""
    
    # Initialize the extractor
    extractor = HanaInformationExtractor()
    
    # Path to sample procedures (assuming they exist in the parent directory)
    sample_dir = project_dir.parent / "sample_procedures"
    
    if not sample_dir.exists():
        print("❌ Sample procedures directory not found")
        return
    
    # Get all SQL files
    sql_files = list(sample_dir.glob("*.sql"))
    
    if not sql_files:
        print("❌ No SQL files found in sample procedures directory")
        return
    
    print(f"🔍 Found {len(sql_files)} SQL files to process")
    print("=" * 60)
    
    results = []
    
    for sql_file in sql_files:
        print(f"\n📁 Processing: {sql_file.name}")
        print("-" * 40)
        
        try:
            # Extract information
            result = extractor.extract_from_file(str(sql_file))
            results.append((sql_file.name, result))
            
            if result.success:
                print(f"✅ Successfully extracted information")
                print(f"   Parsing method: {result.parsing_method}")
                
                proc_info = result.procedure_info
                print(f"   Procedure name: {proc_info.name}")
                print(f"   Language: {proc_info.language}")
                print(f"   Parameters: {proc_info.parameter_count}")
                print(f"   Tables: {proc_info.table_count}")
                print(f"   Complexity score: {proc_info.complexity_score}")
                
                # Show tables
                if proc_info.tables:
                    print(f"   Tables found:")
                    for table_name, table in proc_info.tables.items():
                        columns_count = len(table.columns)
                        usage = ", ".join(table.usage_context) if table.usage_context else "Unknown"
                        print(f"     - {table_name} ({columns_count} columns, used for: {usage})")
                
                # Show parameters
                if proc_info.parameters:
                    print(f"   Parameters:")
                    for param in proc_info.parameters:
                        print(f"     - {param}")
                
                # Show warnings
                if result.warnings:
                    print(f"   ⚠️ Warnings:")
                    for warning in result.warnings:
                        print(f"     - {warning}")
            
            else:
                print(f"❌ Extraction failed")
                if result.errors:
                    print(f"   Errors:")
                    for error in result.errors:
                        print(f"     - {error}")
        
        except Exception as e:
            print(f"❌ Exception occurred: {e}")
            results.append((sql_file.name, None))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 EXTRACTION SUMMARY")
    print("=" * 60)
    
    successful = len([r for _, r in results if r and r.success])
    total = len(results)
    
    print(f"Total files processed: {total}")
    print(f"Successfully extracted: {successful}")
    print(f"Success rate: {(successful/total)*100:.1f}%")
    
    # Consolidated table analysis
    print(f"\n📋 CONSOLIDATED TABLE ANALYSIS")
    print("-" * 40)
    
    table_consolidation = {}
    
    for file_name, result in results:
        if result and result.success:
            proc_info = result.procedure_info
            
            for table_name, table in proc_info.tables.items():
                if table_name not in table_consolidation:
                    table_consolidation[table_name] = {
                        'procedures': [],
                        'total_columns': set(),
                        'usage_contexts': set()
                    }
                
                table_data = table_consolidation[table_name]
                table_data['procedures'].append(proc_info.name)
                table_data['total_columns'].update(table.columns)
                table_data['usage_contexts'].update(table.usage_context)
    
    # Display consolidated results
    for table_name, table_data in sorted(table_consolidation.items()):
        proc_count = len(table_data['procedures'])
        col_count = len(table_data['total_columns'])
        usage = ", ".join(table_data['usage_contexts']) if table_data['usage_contexts'] else "Unknown"
        
        print(f"🗂️ {table_name}")
        print(f"   Used in {proc_count} procedures")
        print(f"   Total columns referenced: {col_count}")
        print(f"   Usage contexts: {usage}")
        print(f"   Procedures: {', '.join(table_data['procedures'])}")
        print()


def extract_from_text_example():
    """Example of extracting from SQL text directly"""
    
    print("\n" + "=" * 60)
    print("📝 EXTRACTING FROM TEXT EXAMPLE")
    print("=" * 60)
    
    # Sample SQL procedure text
    sample_sql = """
    CREATE PROCEDURE PR_SAMPLE_ANALYSIS
    (
        IN IP_DATE_FROM DATE,
        IN IP_DATE_TO DATE,
        OUT OP_RESULT_COUNT INTEGER
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        DECLARE lv_count INTEGER;
        
        SELECT COUNT(*)
        INTO lv_count
        FROM VBAK V
        INNER JOIN VBAP P ON V.VBELN = P.VBELN
        WHERE V.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO;
        
        OP_RESULT_COUNT := lv_count;
    END;
    """
    
    # Initialize extractor
    extractor = HanaInformationExtractor()
    
    # Extract information
    result = extractor.extract_from_sql(sample_sql)
    
    if result.success:
        print("✅ Successfully extracted information from sample SQL")
        
        proc_info = result.procedure_info
        print(f"Procedure name: {proc_info.name}")
        print(f"Parameters: {proc_info.parameter_count}")
        print(f"Tables: {proc_info.table_count}")
        
        # Show detailed information
        print("\nDetailed Information:")
        print(f"Summary: {proc_info.to_summary_dict()}")
    
    else:
        print("❌ Failed to extract information from sample SQL")
        for error in result.errors:
            print(f"Error: {error}")


if __name__ == "__main__":
    print("🔍 SAP HANA Procedure Information Extractor - Example Usage")
    print("=" * 60)
    
    # Extract from sample procedures
    extract_from_sample_procedures()
    
    # Extract from text example
    extract_from_text_example()
    
    print("\n✅ Example completed!")
