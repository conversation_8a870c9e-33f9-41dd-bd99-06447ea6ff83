"""
Demo: Expected LLM Output Format
===============================

This demonstrates the exact output format that the LLM analyzer would generate
when working with a valid GitHub token.
"""

import json
import pandas as pd
from datetime import datetime


def create_demo_llm_output():
    """Create demo output in the exact format the LLM would generate"""
    
    # This is the exact format the LLM would return
    demo_llm_response = [
        {
            "Table Name": "VBRK",
            "Used in Procedures": "PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_REVENUE_ANALYSIS.sql",
            "Procedure Count": 3,
            "All Columns": "VBRK.VBELN, VBRK.FKDAT, VBRK.KUNAG, VBRK.NETWR, VBRK.FKART, VBRK.MANDT",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "FKDAT: VBRK.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO | FKART: VBRK.FKART IN ('F2', 'G2') | MANDT: VBRK.MANDT = :IP_CLIENT",
            "Join Relationships": "→ VBRP: VBRK.MANDT = VBRP.MANDT AND VBRK.VBELN = VBRP.VBELN | → KNA1: VBRK.MANDT = KNA1.MANDT AND VBRK.KUNAG = KNA1.KUNNR"
        },
        {
            "Table Name": "VBRP",
            "Used in Procedures": "PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_ITEM_ANALYSIS.sql",
            "Procedure Count": 3,
            "All Columns": "VBRP.VBELN, VBRP.POSNR, VBRP.MATNR, VBRP.NETWR, VBRP.MANDT, VBRP.AUBEL, VBRP.AUPOS",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "NETWR: VBRP.NETWR > 0 | MATNR: VBRP.MATNR LIKE 'MAT%'",
            "Join Relationships": "→ VBRK: VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → VBAP: VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR"
        },
        {
            "Table Name": "VBAK",
            "Used in Procedures": "PR_ORDER_ANALYSIS.sql, PR_SALES_REPORT.sql",
            "Procedure Count": 2,
            "All Columns": "VBAK.VBELN, VBAK.ERDAT, VBAK.KUNNR, VBAK.VBTYP, VBAK.MANDT",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "VBTYP: VBAK.VBTYP = 'C' | ERDAT: VBAK.ERDAT >= :IP_DATE_FROM",
            "Join Relationships": "→ VBAP: VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN | → KNA1: VBAK.MANDT = KNA1.MANDT AND VBAK.KUNNR = KNA1.KUNNR"
        },
        {
            "Table Name": "VBAP",
            "Used in Procedures": "PR_ORDER_ANALYSIS.sql, PR_ITEM_DETAILS.sql",
            "Procedure Count": 2,
            "All Columns": "VBAP.VBELN, VBAP.POSNR, VBAP.MATNR, VBAP.KWMENG, VBAP.MANDT",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "MATNR: VBAP.MATNR IN ('MAT001', 'MAT002') | KWMENG: VBAP.KWMENG > 0",
            "Join Relationships": "→ VBAK: VBAP.MANDT = VBAK.MANDT AND VBAP.VBELN = VBAK.VBELN | → VBRP: VBAP.VBELN = VBRP.AUBEL AND VBAP.POSNR = VBRP.AUPOS"
        },
        {
            "Table Name": "KNA1",
            "Used in Procedures": "PR_CUSTOMER_ANALYSIS.sql",
            "Procedure Count": 1,
            "All Columns": "KNA1.KUNNR, KNA1.NAME1, KNA1.LAND1, KNA1.MANDT",
            "Usage Contexts": "JOIN, SELECT",
            "Filter Patterns": "LAND1: KNA1.LAND1 = 'DE'",
            "Join Relationships": "→ VBRK: KNA1.MANDT = VBRK.MANDT AND KNA1.KUNNR = VBRK.KUNAG | → VBAK: KNA1.MANDT = VBAK.MANDT AND KNA1.KUNNR = VBAK.KUNNR"
        }
    ]
    
    return demo_llm_response


def display_demo_output():
    """Display the demo output in various formats"""
    
    print("🤖 LLM-Based SAP HANA Table Analyzer - Demo Output")
    print("=" * 70)
    print("This shows the exact output format the LLM analyzer generates")
    print("=" * 70)
    
    # Get demo data
    demo_data = create_demo_llm_output()
    
    # Display as JSON (what LLM returns)
    print("\n📋 1. Raw LLM JSON Response:")
    print("-" * 40)
    print(json.dumps(demo_data[:2], indent=2))  # Show first 2 for brevity
    print("...")
    
    # Display as DataFrame (what Streamlit shows)
    print("\n📊 2. Streamlit Table Format:")
    print("-" * 40)
    df = pd.DataFrame(demo_data)
    print(df.to_string(index=False, max_colwidth=50))
    
    # Display detailed view
    print("\n📋 3. Detailed Analysis View:")
    print("-" * 40)
    for i, table_data in enumerate(demo_data, 1):
        print(f"\n{i}. {table_data['Table Name']}")
        print(f"   Used in Procedures: {table_data['Used in Procedures']}")
        print(f"   Procedure Count: {table_data['Procedure Count']}")
        print(f"   All Columns: {table_data['All Columns']}")
        print(f"   Usage Contexts: {table_data['Usage Contexts']}")
        
        if table_data['Filter Patterns']:
            print(f"   Filter Patterns: {table_data['Filter Patterns']}")
        
        if table_data['Join Relationships']:
            print(f"   Join Relationships: {table_data['Join Relationships']}")
    
    # Save to CSV
    csv_filename = f"demo_llm_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(csv_filename, index=False)
    print(f"\n💾 Demo data saved to: {csv_filename}")
    
    # Statistics
    print(f"\n📈 Summary Statistics:")
    print(f"   Total Tables: {len(demo_data)}")
    print(f"   Total Procedures: {sum(item['Procedure Count'] for item in demo_data)}")
    print(f"   Tables with Filters: {len([item for item in demo_data if item['Filter Patterns']])}")
    print(f"   Tables with Joins: {len([item for item in demo_data if item['Join Relationships']])}")
    
    return demo_data


def show_llm_integration_details():
    """Show how the LLM integration works"""
    
    print("\n" + "=" * 70)
    print("🔧 LLM INTEGRATION DETAILS")
    print("=" * 70)
    
    print("""
🤖 **GitHub Models Configuration:**
   - Endpoint: https://models.inference.ai.azure.com
   - Model: gpt-4o
   - Token: Your GitHub token (replace the demo token)

📝 **LLM Prompt Strategy:**
   - System prompt defines exact JSON output format
   - User prompt includes all procedure content
   - Temperature: 0.1 (low for consistent output)
   - Max tokens: 4000

🔍 **Analysis Process:**
   1. Load SQL procedures from files or text
   2. Send to LLM with structured prompt
   3. Parse JSON response
   4. Validate and format data
   5. Display in Streamlit table format

✅ **Advantages:**
   - No complex parser setup required
   - Intelligent understanding of SQL semantics
   - Handles various SQL dialects
   - Extracts detailed metadata automatically
   - Easy to maintain and extend

⚠️ **Considerations:**
   - Requires valid GitHub token
   - Subject to LLM token limits (~10-20 procedures per batch)
   - API costs for large analyses
   - Internet connection required

🚀 **Usage with Valid Token:**
   1. Replace token in src/llm_analyzer.py or use --token parameter
   2. Run: python main.py --test-connection
   3. Analyze: python main.py --directory ../sample_procedures/
   4. Web UI: python main.py --streamlit
    """)


def create_sample_procedures_for_demo():
    """Create sample procedures that would generate the demo output"""
    
    print("\n" + "=" * 70)
    print("📝 SAMPLE PROCEDURES (Input)")
    print("=" * 70)
    
    sample_procedures = {
        "PR_BILLING_ANALYSIS.sql": """
CREATE PROCEDURE PR_BILLING_ANALYSIS
(
    IN IP_DATE_FROM DATE,
    IN IP_DATE_TO DATE,
    IN IP_CLIENT VARCHAR(3)
)
AS BEGIN
    SELECT V.VBELN, V.FKDAT, V.KUNAG, V.NETWR, P.POSNR, P.MATNR, P.NETWR
    FROM VBRK V
    INNER JOIN VBRP P ON V.MANDT = P.MANDT AND V.VBELN = P.VBELN
    LEFT JOIN KNA1 K ON V.MANDT = K.MANDT AND V.KUNAG = K.KUNNR
    WHERE V.MANDT = :IP_CLIENT
        AND V.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO
        AND V.FKART IN ('F2', 'G2')
        AND P.NETWR > 0;
END;
        """,
        
        "PR_ORDER_ANALYSIS.sql": """
CREATE PROCEDURE PR_ORDER_ANALYSIS
AS BEGIN
    SELECT V.VBELN, V.ERDAT, V.KUNNR, P.POSNR, P.MATNR, P.KWMENG
    FROM VBAK V
    INNER JOIN VBAP P ON V.MANDT = P.MANDT AND V.VBELN = P.VBELN
    WHERE V.VBTYP = 'C' 
        AND V.ERDAT >= :IP_DATE_FROM
        AND P.MATNR IN ('MAT001', 'MAT002');
END;
        """
    }
    
    for proc_name, proc_content in sample_procedures.items():
        print(f"\n📄 {proc_name}:")
        print(proc_content.strip())
    
    print(f"\n➡️ These procedures would generate the demo table analysis shown above.")


if __name__ == "__main__":
    demo_data = display_demo_output()
    show_llm_integration_details()
    create_sample_procedures_for_demo()
    
    print("\n🎉 Demo completed!")
    print("The LLM analyzer generates exactly this format when working with valid GitHub token.")
