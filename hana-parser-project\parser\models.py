"""
Data Models for SAP HANA SQL Procedure Information Extraction
============================================================

This module defines the data structures used to represent extracted information
from SAP HANA SQL procedures using ANTLR parser.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Set, Optional, Any
from enum import Enum


class ParameterDirection(Enum):
    """Parameter direction enumeration"""
    IN = "IN"
    OUT = "OUT"
    INOUT = "INOUT"


class JoinType(Enum):
    """Join type enumeration"""
    INNER = "INNER"
    LEFT = "LEFT"
    RIGHT = "RIGHT"
    FULL = "FULL"
    CROSS = "CROSS"


@dataclass
class Parameter:
    """Represents a procedure parameter"""
    name: str
    data_type: str
    direction: ParameterDirection = ParameterDirection.IN
    default_value: Optional[str] = None
    is_table_type: bool = False
    
    def __str__(self) -> str:
        return f"{self.direction.value} {self.name} {self.data_type}"


@dataclass
class Column:
    """Represents a table column reference"""
    name: str
    table_alias: Optional[str] = None
    table_name: Optional[str] = None
    is_aggregate: bool = False
    aggregate_function: Optional[str] = None
    
    @property
    def full_reference(self) -> str:
        """Get full column reference (table.column)"""
        table_ref = self.table_alias or self.table_name
        return f"{table_ref}.{self.name}" if table_ref else self.name


@dataclass
class Table:
    """Represents a table reference in the procedure"""
    name: str
    alias: Optional[str] = None
    schema: Optional[str] = None
    columns: Set[str] = field(default_factory=set)
    usage_context: Set[str] = field(default_factory=set)  # SELECT, INSERT, UPDATE, DELETE
    
    @property
    def full_name(self) -> str:
        """Get full table name with schema"""
        return f"{self.schema}.{self.name}" if self.schema else self.name
    
    @property
    def reference_name(self) -> str:
        """Get the name used to reference this table (alias or name)"""
        return self.alias or self.name


@dataclass
class FilterCondition:
    """Represents a WHERE clause filter condition"""
    table: str
    column: str
    operator: str
    values: List[str]
    condition_text: str
    is_parameter: bool = False
    parameter_name: Optional[str] = None
    
    def __str__(self) -> str:
        return f"{self.table}.{self.column} {self.operator} {', '.join(self.values)}"


@dataclass
class JoinCondition:
    """Represents a JOIN relationship between tables"""
    left_table: str
    right_table: str
    join_type: JoinType
    condition: str
    left_columns: List[str] = field(default_factory=list)
    right_columns: List[str] = field(default_factory=list)
    
    def __str__(self) -> str:
        return f"{self.left_table} {self.join_type.value} JOIN {self.right_table} ON {self.condition}"


@dataclass
class SQLStatement:
    """Represents a SQL statement within the procedure"""
    statement_type: str  # SELECT, INSERT, UPDATE, DELETE, TRUNCATE, etc.
    tables: List[Table] = field(default_factory=list)
    columns: List[Column] = field(default_factory=list)
    filters: List[FilterCondition] = field(default_factory=list)
    joins: List[JoinCondition] = field(default_factory=list)
    raw_sql: str = ""
    
    @property
    def table_names(self) -> Set[str]:
        """Get all table names referenced in this statement"""
        return {table.name for table in self.tables}


@dataclass
class Variable:
    """Represents a declared variable in the procedure"""
    name: str
    data_type: str
    is_constant: bool = False
    default_value: Optional[str] = None
    is_table_variable: bool = False
    
    def __str__(self) -> str:
        const_str = "CONSTANT " if self.is_constant else ""
        return f"DECLARE {self.name} {const_str}{self.data_type}"


@dataclass
class ProcedureInfo:
    """Complete information extracted from a SAP HANA procedure"""
    name: str
    parameters: List[Parameter] = field(default_factory=list)
    variables: List[Variable] = field(default_factory=list)
    tables: Dict[str, Table] = field(default_factory=dict)
    statements: List[SQLStatement] = field(default_factory=list)
    all_filters: List[FilterCondition] = field(default_factory=list)
    all_joins: List[JoinCondition] = field(default_factory=list)
    language: str = "SQLSCRIPT"
    security_mode: Optional[str] = None
    default_schema: Optional[str] = None
    raw_sql: str = ""
    
    @property
    def table_count(self) -> int:
        """Get total number of unique tables"""
        return len(self.tables)
    
    @property
    def parameter_count(self) -> int:
        """Get total number of parameters"""
        return len(self.parameters)
    
    @property
    def complexity_score(self) -> int:
        """Calculate a complexity score based on various factors"""
        score = 0
        score += len(self.tables) * 2  # Tables
        score += len(self.all_joins) * 3  # Joins are more complex
        score += len(self.all_filters) * 1  # Filters
        score += len(self.parameters) * 1  # Parameters
        score += len(self.statements) * 2  # SQL statements
        return score
    
    def get_table_by_name_or_alias(self, name: str) -> Optional[Table]:
        """Get table by name or alias"""
        for table in self.tables.values():
            if table.name == name or table.alias == name:
                return table
        return None
    
    def get_input_parameters(self) -> List[Parameter]:
        """Get only input parameters"""
        return [p for p in self.parameters if p.direction == ParameterDirection.IN]
    
    def get_output_parameters(self) -> List[Parameter]:
        """Get only output parameters"""
        return [p for p in self.parameters if p.direction == ParameterDirection.OUT]
    
    def get_tables_by_usage(self, usage: str) -> List[Table]:
        """Get tables by usage context (SELECT, INSERT, UPDATE, DELETE)"""
        return [table for table in self.tables.values() if usage in table.usage_context]
    
    def to_summary_dict(self) -> Dict[str, Any]:
        """Convert to a summary dictionary for easy serialization"""
        return {
            'procedure_name': self.name,
            'language': self.language,
            'security_mode': self.security_mode,
            'default_schema': self.default_schema,
            'parameter_count': self.parameter_count,
            'table_count': self.table_count,
            'statement_count': len(self.statements),
            'join_count': len(self.all_joins),
            'filter_count': len(self.all_filters),
            'complexity_score': self.complexity_score,
            'tables': [table.name for table in self.tables.values()],
            'parameters': [str(param) for param in self.parameters],
            'input_parameters': [p.name for p in self.get_input_parameters()],
            'output_parameters': [p.name for p in self.get_output_parameters()]
        }


@dataclass
class ExtractionResult:
    """Result of the extraction process"""
    success: bool
    procedure_info: Optional[ProcedureInfo] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    parsing_method: str = "ANTLR"  # ANTLR or FALLBACK
    
    def add_error(self, error: str):
        """Add an error message"""
        self.errors.append(error)
        self.success = False
    
    def add_warning(self, warning: str):
        """Add a warning message"""
        self.warnings.append(warning)
