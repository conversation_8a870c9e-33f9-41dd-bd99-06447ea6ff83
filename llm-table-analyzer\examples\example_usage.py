"""
Example Usage: LLM-Based SAP HANA Table Analyzer
===============================================

This example demonstrates how to use the LLM-based analyzer to generate
table usage metadata from SAP HANA procedures.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path to import our modules
current_dir = Path(__file__).parent
project_dir = current_dir.parent
src_dir = project_dir / "src"
sys.path.insert(0, str(src_dir))

from llm_analyzer import GitHubModelsLLMAnalyzer, <PERSON>cedure<PERSON>oa<PERSON>


def example_single_procedure():
    """Example with a single procedure"""
    
    print("🤖 Example 1: Single Procedure Analysis")
    print("=" * 50)
    
    # Sample SAP HANA procedure
    sample_procedure = """
    CREATE PROCEDURE PR_BILLING_ANALYSIS
    (
        IN IP_DATE_FROM DATE,
        IN IP_DATE_TO DATE,
        IN IP_CLIENT VARCHAR(3)
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        -- Clear target table
        TRUNCATE TABLE CT_BILLING_RESULT;
        
        -- Main analysis query
        INSERT INTO CT_BILLING_RESULT
        SELECT 
            V.VBELN as BILLING_DOC,
            V.FKDAT as BILLING_DATE,
            V.KUNAG as CUSTOMER,
            V.NETWR as NET_VALUE,
            P.POSNR as ITEM,
            P.MATNR as MATERIAL,
            P.NETWR as ITEM_VALUE
        FROM VBRK V
        INNER JOIN VBRP P 
            ON V.MANDT = P.MANDT 
            AND V.VBELN = P.VBELN
        LEFT JOIN KNA1 K 
            ON V.MANDT = K.MANDT 
            AND V.KUNAG = K.KUNNR
        WHERE V.MANDT = :IP_CLIENT
            AND V.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO
            AND V.FKART IN ('F2', 'G2')
            AND P.NETWR > 0;
    END;
    """
    
    # Create procedure data
    procedures = [
        ProcedureLoader.create_from_text("PR_BILLING_ANALYSIS", sample_procedure)
    ]
    
    # Initialize analyzer
    analyzer = GitHubModelsLLMAnalyzer()
    
    # Test connection
    if not analyzer.test_connection():
        print("❌ LLM connection failed")
        return
    
    # Analyze
    result = analyzer.analyze_procedures(procedures)
    
    if result.success:
        print(f"✅ Analysis successful!")
        print(f"📊 Found {result.total_tables} tables")
        
        # Display results
        print("\n📋 Table Analysis:")
        for table_data in result.table_analysis:
            print(f"\n🗂️ {table_data['Table Name']}")
            print(f"   Columns: {table_data['All Columns']}")
            print(f"   Usage: {table_data['Usage Contexts']}")
            
            if table_data['Filter Patterns']:
                print(f"   Filters: {table_data['Filter Patterns']}")
            
            if table_data['Join Relationships']:
                print(f"   Joins: {table_data['Join Relationships']}")
    
    else:
        print(f"❌ Analysis failed: {result.error}")


def example_multiple_procedures():
    """Example with multiple procedures"""
    
    print("\n🤖 Example 2: Multiple Procedures Analysis")
    print("=" * 50)
    
    # Sample procedures
    procedures = [
        ProcedureLoader.create_from_text("PR_ORDER_ANALYSIS", """
        CREATE PROCEDURE PR_ORDER_ANALYSIS()
        AS BEGIN
            SELECT V.VBELN, V.ERDAT, P.POSNR, P.MATNR
            FROM VBAK V
            INNER JOIN VBAP P ON V.VBELN = P.VBELN
            WHERE V.VBTYP = 'C' AND P.MATNR LIKE 'MAT%';
        END;
        """),
        
        ProcedureLoader.create_from_text("PR_DELIVERY_ANALYSIS", """
        CREATE PROCEDURE PR_DELIVERY_ANALYSIS()
        AS BEGIN
            SELECT L.VBELN, L.LFDAT, P.POSNR
            FROM LIKP L
            INNER JOIN LIPS P ON L.VBELN = P.VBELN
            LEFT JOIN VBAP V ON P.VGBEL = V.VBELN
            WHERE L.LFDAT >= '2024-01-01';
        END;
        """)
    ]
    
    # Initialize analyzer
    analyzer = GitHubModelsLLMAnalyzer()
    
    # Analyze
    result = analyzer.analyze_procedures(procedures)
    
    if result.success:
        print(f"✅ Analysis successful!")
        print(f"📊 Analyzed {result.procedure_count} procedures")
        print(f"📊 Found {result.total_tables} unique tables")
        
        # Display consolidated results
        print("\n📋 Consolidated Table Analysis:")
        for i, table_data in enumerate(result.table_analysis, 1):
            print(f"\n{i}. {table_data['Table Name']}")
            print(f"   Used in: {table_data['Used in Procedures']}")
            print(f"   Procedure Count: {table_data['Procedure Count']}")
            print(f"   Columns: {table_data['All Columns']}")
    
    else:
        print(f"❌ Analysis failed: {result.error}")


def example_from_files():
    """Example loading from actual files"""
    
    print("\n🤖 Example 3: Loading from Files")
    print("=" * 50)
    
    # Check if sample procedures exist
    sample_dir = project_dir.parent / "sample_procedures"
    
    if sample_dir.exists():
        print(f"📁 Loading procedures from: {sample_dir}")
        
        # Load procedures
        procedures = ProcedureLoader.load_from_directory(str(sample_dir))
        
        if procedures:
            print(f"🔍 Found {len(procedures)} procedures")
            
            # Limit to avoid token limits
            if len(procedures) > 3:
                procedures = procedures[:3]
                print(f"⚠️ Limiting to first 3 procedures for demo")
            
            # Initialize analyzer
            analyzer = GitHubModelsLLMAnalyzer()
            
            # Analyze
            result = analyzer.analyze_procedures(procedures)
            
            if result.success:
                print(f"✅ Analysis successful!")
                print(f"📊 Found {result.total_tables} tables")
                
                # Show top tables
                print("\n🏆 Top Tables:")
                for table_data in result.table_analysis[:5]:
                    print(f"   {table_data['Table Name']} - {table_data['Procedure Count']} procedures")
            
            else:
                print(f"❌ Analysis failed: {result.error}")
        
        else:
            print("❌ No procedures found in sample directory")
    
    else:
        print("❌ Sample procedures directory not found")


def example_save_results():
    """Example saving results to file"""
    
    print("\n🤖 Example 4: Saving Results")
    print("=" * 50)
    
    # Simple procedure for demo
    procedures = [
        ProcedureLoader.create_from_text("PR_DEMO", """
        CREATE PROCEDURE PR_DEMO() AS BEGIN
            SELECT * FROM VBAK WHERE VBTYP = 'C';
        END;
        """)
    ]
    
    # Analyze
    analyzer = GitHubModelsLLMAnalyzer()
    result = analyzer.analyze_procedures(procedures)
    
    if result.success:
        # Save to JSON
        import json
        from datetime import datetime
        
        output_file = f"demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_data = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'procedure_count': result.procedure_count,
                'total_tables': result.total_tables
            },
            'table_analysis': result.table_analysis
        }
        
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"💾 Results saved to: {output_file}")
    
    else:
        print(f"❌ Analysis failed: {result.error}")


if __name__ == "__main__":
    print("🤖 LLM-Based SAP HANA Table Analyzer - Examples")
    print("=" * 60)
    
    try:
        example_single_procedure()
        example_multiple_procedures()
        example_from_files()
        example_save_results()
        
        print("\n✅ All examples completed!")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        import traceback
        traceback.print_exc()
