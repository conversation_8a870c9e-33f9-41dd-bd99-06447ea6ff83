"""
Test Enhanced Metadata Extraction
=================================

Test the enhanced filter patterns and join relationships extraction.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from parser import Han<PERSON>I<PERSON><PERSON>Extractor


def test_enhanced_extraction():
    """Test enhanced extraction with a well-formed procedure"""
    
    sample_sql = """
    CREATE PROCEDURE PR_ENHANCED_TEST
    (
        IN IP_DATE_FROM DATE,
        IN IP_DATE_TO DATE,
        IN IP_CLIENT VARCHAR(3),
        OUT OP_COUNT INTEGER
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        DECLARE lv_count INTEGER;
        
        -- Clear target table
        TRUNCATE TABLE CT_RESULT;
        
        -- Main query with joins and filters
        INSERT INTO CT_RESULT
        SELECT 
            V.VBELN,
            V.ERDAT,
            V<PERSON>,
            <PERSON><PERSON>,
            P.MATNR,
            P.NETWR,
            B.FKDAT,
            B.NETWR as BILLING_AMOUNT
        FROM VBAK V
        INNER JOIN VBAP P 
            ON V.MANDT = P.MANDT 
            AND V.VBELN = P.VBELN
        LEFT JOIN VBRP B 
            ON P.MANDT = B.MANDT 
            AND P.VBELN = B.AUBEL 
            AND P.POSNR = B.AUPOS
        WHERE V.MANDT = :IP_CLIENT
            AND V.ERDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO
            AND V.VBTYP = 'C'
            AND P.MATNR IN ('MAT001', 'MAT002', 'MAT003')
            AND B.FKART IN ('F2', 'G2');
        
        -- Get count
        SELECT COUNT(*) INTO lv_count FROM CT_RESULT;
        OP_COUNT := lv_count;
        
    END;
    """
    
    print("🧪 Testing Enhanced Metadata Extraction")
    print("=" * 50)
    
    # Initialize extractor
    extractor = HanaInformationExtractor()
    
    # Extract information
    result = extractor.extract_from_sql(sample_sql)
    
    if result.success:
        proc_info = result.procedure_info
        
        print(f"✅ Procedure: {proc_info.name}")
        print(f"✅ Parsing method: {result.parsing_method}")
        print(f"✅ Tables found: {proc_info.table_count}")
        print(f"✅ Parameters: {proc_info.parameter_count}")
        print(f"✅ Filters: {len(proc_info.all_filters)}")
        print(f"✅ Joins: {len(proc_info.all_joins)}")
        
        print("\n📋 DETAILED ANALYSIS")
        print("-" * 30)
        
        # Show tables with columns
        print("🗂️ Tables and Columns:")
        for table_name, table in proc_info.tables.items():
            columns = list(table.columns) if table.columns else ["No columns detected"]
            usage = list(table.usage_context) if table.usage_context else ["Unknown"]
            print(f"   {table_name}: {len(columns)} columns, Usage: {', '.join(usage)}")
            if len(columns) <= 10:  # Show columns if not too many
                print(f"      Columns: {', '.join(columns)}")
        
        # Show filter conditions
        print("\n🔍 Filter Conditions:")
        if proc_info.all_filters:
            for filter_cond in proc_info.all_filters:
                print(f"   {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {', '.join(filter_cond.values)}")
        else:
            print("   No filters detected")
        
        # Show join relationships
        print("\n🔗 Join Relationships:")
        if proc_info.all_joins:
            for join_cond in proc_info.all_joins:
                print(f"   {join_cond.left_table} {join_cond.join_type.value} JOIN {join_cond.right_table}")
                print(f"      Condition: {join_cond.condition}")
        else:
            print("   No joins detected")
        
        # Show parameters
        print("\n📝 Parameters:")
        for param in proc_info.parameters:
            print(f"   {param}")
        
        return True
    
    else:
        print("❌ Extraction failed:")
        for error in result.errors:
            print(f"   Error: {error}")
        return False


def test_streamlit_format():
    """Test the format that would be generated for Streamlit table"""
    
    print("\n" + "=" * 50)
    print("📊 STREAMLIT TABLE FORMAT TEST")
    print("=" * 50)
    
    # This simulates what the Streamlit app would generate
    sample_sql = """
    CREATE PROCEDURE PR_TABLE_FORMAT_TEST
    (
        IN IP_CLIENT VARCHAR(3)
    )
    LANGUAGE SQLSCRIPT
    AS
    BEGIN
        SELECT V.VBELN, P.MATNR
        FROM VBAK V
        INNER JOIN VBAP P ON V.VBELN = P.VBELN AND V.MANDT = P.MANDT
        WHERE V.MANDT = :IP_CLIENT
            AND V.VBTYP = 'C'
            AND P.MATNR IN ('MAT001', 'MAT002');
    END;
    """
    
    extractor = HanaInformationExtractor()
    result = extractor.extract_from_sql(sample_sql)
    
    if result.success:
        proc_info = result.procedure_info
        
        # Simulate the consolidated analysis that Streamlit would generate
        table_consolidation = {}
        proc_name = proc_info.name
        
        for table_name, table in proc_info.tables.items():
            if table_name not in table_consolidation:
                table_consolidation[table_name] = {
                    'used_in_procedures': [],
                    'procedure_count': 0,
                    'all_columns': set(),
                    'usage_contexts': set(),
                    'filter_patterns': {},
                    'join_relationships': []
                }
            
            table_data = table_consolidation[table_name]
            table_data['used_in_procedures'].append(proc_name)
            table_data['procedure_count'] += 1
            table_data['all_columns'].update(table.columns)
            table_data['usage_contexts'].update(table.usage_context)
            
            # Collect filter patterns
            for filter_cond in proc_info.all_filters:
                if filter_cond.table == table_name:
                    column = filter_cond.column
                    if column not in table_data['filter_patterns']:
                        table_data['filter_patterns'][column] = []
                    
                    filter_text = f"{filter_cond.column}: {filter_cond.condition_text}"
                    if filter_text not in table_data['filter_patterns'][column]:
                        table_data['filter_patterns'][column].append(filter_text)
            
            # Collect join relationships
            for join_cond in proc_info.all_joins:
                if join_cond.left_table == table_name or join_cond.right_table == table_name:
                    join_text = f"→ {join_cond.right_table if join_cond.left_table == table_name else join_cond.left_table}: {join_cond.condition}"
                    if join_text not in table_data['join_relationships']:
                        table_data['join_relationships'].append(join_text)
        
        # Display in the format that would appear in Streamlit table
        print("Table Analysis (Streamlit Format):")
        print("-" * 40)
        
        for table_name, table_data in table_consolidation.items():
            procedures_str = ", ".join(table_data['used_in_procedures'])
            columns_list = [f"{table_name}.{col}" for col in sorted(table_data['all_columns'])]
            columns_str = ", ".join(columns_list)
            usage_contexts_str = ", ".join(sorted(table_data['usage_contexts']))
            
            # Format filter patterns
            filter_patterns = []
            for column, conditions in table_data['filter_patterns'].items():
                for condition in conditions:
                    clean_condition = condition.replace(f"{column}: {table_name}.{column} ", "")
                    filter_patterns.append(f"{column}: {clean_condition}")
            filter_patterns_str = " | ".join(filter_patterns)
            
            # Format join relationships
            join_relationships_str = " | ".join(table_data['join_relationships'])
            
            print(f"\n🗂️ {table_name}")
            print(f"   Used in Procedures: {procedures_str}")
            print(f"   All Columns: {columns_str}")
            print(f"   Usage Contexts: {usage_contexts_str}")
            print(f"   Filter Patterns: {filter_patterns_str}")
            print(f"   Join Relationships: {join_relationships_str}")
        
        return True
    
    else:
        print("❌ Test failed")
        return False


if __name__ == "__main__":
    success1 = test_enhanced_extraction()
    success2 = test_streamlit_format()
    
    if success1 and success2:
        print("\n🎉 All enhanced extraction tests passed!")
    else:
        print("\n⚠️ Some tests failed")
