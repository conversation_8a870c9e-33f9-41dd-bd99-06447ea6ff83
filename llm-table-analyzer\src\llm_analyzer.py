"""
LLM-Based SAP HANA Table Analyzer
=================================

This module uses GitHub Models endpoint to analyze SAP HANA SQL procedures
and generate detailed table usage metadata without using ANTLR parser.
"""

import os
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from openai import OpenAI


@dataclass
class AnalysisResult:
    """Result of enhanced LLM analysis"""
    success: bool
    table_analysis: Optional[List[Dict[str, Any]]] = None
    raw_response: Optional[str] = None
    error: Optional[str] = None
    procedure_count: int = 0
    total_tables: int = 0
    total_views: int = 0
    total_schemas: int = 0
    filter_conflicts_detected: int = 0


class GitHubModelsLLMAnalyzer:
    """LLM-based analyzer using GitHub Models endpoint"""
    
    def __init__(self, token: str = None):
        """Initialize the LLM analyzer"""
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"
        self.token = token or "****************************************"
        
        self.client = OpenAI(
            base_url=self.endpoint,
            api_key=self.token,
        )
        
        print(f"🤖 LLM Analyzer initialized with GitHub Models endpoint")
    
    def analyze_procedures(self, procedures: List[Dict[str, str]]) -> AnalysisResult:
        """Analyze multiple procedures and generate table usage metadata"""
        
        try:
            # Prepare the prompt with all procedures
            prompt = self._create_analysis_prompt(procedures)
            
            # Call the LLM
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent output
                top_p=0.9,
                model=self.model,
                max_tokens=4000
            )
            
            raw_response = response.choices[0].message.content
            
            # Parse the LLM response
            table_analysis = self._parse_llm_response(raw_response)

            # Calculate enhanced statistics
            total_tables = 0
            total_views = 0
            total_schemas = set()
            filter_conflicts = 0

            if table_analysis:
                for item in table_analysis:
                    obj_type = item.get("Object Type", "TABLE")
                    if obj_type == "TABLE":
                        total_tables += 1
                    elif "VIEW" in obj_type:
                        total_views += 1

                    schema = item.get("Schema", "")
                    if schema:
                        total_schemas.add(schema)

                    if "conflict" in item.get("Filter Conflict Analysis", "").lower():
                        filter_conflicts += 1

            return AnalysisResult(
                success=True,
                table_analysis=table_analysis,
                raw_response=raw_response,
                procedure_count=len(procedures),
                total_tables=total_tables,
                total_views=total_views,
                total_schemas=len(total_schemas),
                filter_conflicts_detected=filter_conflicts
            )
            
        except Exception as e:
            return AnalysisResult(
                success=False,
                error=str(e)
            )
    
    def _get_system_prompt(self) -> str:
        """Get the enhanced system prompt for intelligent filter analysis"""
        return """You are an expert SAP HANA SQL analyst. Your task is to analyze SAP HANA SQL procedures and generate a detailed table/view/schema usage analysis with intelligent filter consolidation.

You must output ONLY a valid JSON array with the following exact structure for each table/view:

[
  {
    "Table Name": "SCHEMA.TABLE_NAME or VIEW_NAME",
    "Object Type": "TABLE or VIEW or CDS_VIEW",
    "Schema": "SCHEMA_NAME",
    "Used in Procedures": "procedure1, procedure2, procedure3",
    "Procedure Count": 3,
    "All Columns": "TABLE.COLUMN1, TABLE.COLUMN2, TABLE.COLUMN3",
    "Usage Contexts": "FILTER, JOIN, SELECT, INSERT, UPDATE",
    "Filter Patterns": "COLUMN1: [PROC1: condition1, PROC2: ALL_VALUES] | COLUMN2: [ALL_PROCS: condition2]",
    "Recommended Filter Strategy": "USE_ALL_VALUES or USE_RESTRICTIVE_FILTERS",
    "Filter Conflict Analysis": "PROC1,PROC2 use filters, PROC3 needs all values - recommend ALL_VALUES",
    "Join Relationships": "→ TABLE2: join_condition | → TABLE3: join_condition"
  }
]

CRITICAL ANALYSIS RULES:
1. **Schema Detection**: Always include schema names (e.g., "SAP_SCHEMA.VBAK", "_SYS_BIC.VIEW_NAME")
2. **Object Type Classification**: Identify if it's TABLE, VIEW, CDS_VIEW, or CALCULATION_VIEW
3. **Intelligent Filter Analysis**:
   - If some procedures filter a column and others need all values → recommend "USE_ALL_VALUES"
   - If all procedures use same filter → recommend "USE_RESTRICTIVE_FILTERS"
   - Show per-procedure filter usage: [PROC1: condition, PROC2: ALL_VALUES]
4. **Filter Conflict Detection**: Analyze when procedures have conflicting filter needs
5. **Least Restrictive Logic**: When in doubt, recommend getting all values rather than restricting
6. **View Recognition**: Detect calculation views, CDS views, and database views
7. **Complete Schema Mapping**: Include full schema.object names

FILTER PATTERN FORMAT:
- "COLUMN: [PROC1: MANDT='100', PROC2: ALL_VALUES] | STATUS: [ALL_PROCS: IN('A','B')]"
- Use "ALL_VALUES" when procedure needs unrestricted data
- Use "ALL_PROCS" when all procedures use same filter

OUTPUT REQUIREMENTS:
- ONLY valid JSON, no explanations
- One row per unique schema.table/view combination
- Focus on SAP objects: tables (VBAK, VBAP, etc.), views, CDS views
- Include system schemas: _SYS_BIC, SAP_*, etc."""
    
    def _create_analysis_prompt(self, procedures: List[Dict[str, str]]) -> str:
        """Create the analysis prompt with procedure content"""
        
        prompt = "Analyze the following SAP HANA SQL procedures and generate table usage metadata:\n\n"
        
        for i, proc in enumerate(procedures, 1):
            prompt += f"=== PROCEDURE {i}: {proc['name']} ===\n"
            prompt += f"{proc['content']}\n\n"
        
        prompt += """
Generate a consolidated table/view/schema analysis with intelligent filter consolidation.

CRITICAL ANALYSIS REQUIREMENTS:
1. **Schema Detection**: Extract full schema.table names (e.g., "_SYS_BIC.VIEW_NAME", "SAP_SCHEMA.VBAK")
2. **Object Classification**: Identify TABLE, VIEW, CDS_VIEW, CALCULATION_VIEW
3. **Intelligent Filter Analysis**:
   - Analyze each column's filter usage across ALL procedures
   - If PROC1 filters MANDT='100' but PROC2 needs all MANDT values → recommend "USE_ALL_VALUES"
   - If all procedures use same filter → recommend "USE_RESTRICTIVE_FILTERS"
   - Show per-procedure filter patterns: [PROC1: condition, PROC2: ALL_VALUES]
4. **Filter Conflict Detection**: Identify when procedures have conflicting filter needs
5. **Least Restrictive Strategy**: When procedures conflict, recommend getting all values
6. **Complete Metadata**: Include schema names, object types, filter strategies

EXAMPLE FILTER ANALYSIS:
- If PROC1 has "WHERE MANDT = '100'" and PROC2 has no MANDT filter
- Output: "MANDT: [PROC1: ='100', PROC2: ALL_VALUES]"
- Recommendation: "USE_ALL_VALUES"
- Conflict Analysis: "PROC1 restricts MANDT, PROC2 needs all - recommend ALL_VALUES"

Output only the JSON array with enhanced metadata, no other text."""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse the LLM response to extract table analysis"""
        
        try:
            # Clean the response to extract JSON
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            table_analysis = json.loads(cleaned_response)
            
            # Validate the structure
            if isinstance(table_analysis, list):
                validated_analysis = []
                for item in table_analysis:
                    if isinstance(item, dict) and "Table Name" in item:
                        # Ensure all required fields exist with enhanced metadata
                        validated_item = {
                            "Table Name": item.get("Table Name", ""),
                            "Object Type": item.get("Object Type", "TABLE"),
                            "Schema": item.get("Schema", ""),
                            "Used in Procedures": item.get("Used in Procedures", ""),
                            "Procedure Count": item.get("Procedure Count", 0),
                            "All Columns": item.get("All Columns", ""),
                            "Usage Contexts": item.get("Usage Contexts", ""),
                            "Filter Patterns": item.get("Filter Patterns", ""),
                            "Recommended Filter Strategy": item.get("Recommended Filter Strategy", ""),
                            "Filter Conflict Analysis": item.get("Filter Conflict Analysis", ""),
                            "Join Relationships": item.get("Join Relationships", "")
                        }
                        validated_analysis.append(validated_item)
                
                return validated_analysis
            
            return []
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"Response: {response[:500]}...")
            return []
        except Exception as e:
            print(f"❌ Error parsing LLM response: {e}")
            return []
    
    def _clean_json_response(self, response: str) -> str:
        """Clean the LLM response to extract valid JSON"""
        
        # Remove markdown code blocks
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)
        
        # Find JSON array boundaries
        start_idx = response.find('[')
        end_idx = response.rfind(']')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        # If no array found, try to find object and wrap in array
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return f"[{response[start_idx:end_idx + 1]}]"
        
        return response
    
    def test_connection(self) -> bool:
        """Test the LLM connection"""
        
        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say 'Connection successful' if you can read this."}
                ],
                temperature=0.1,
                model=self.model,
                max_tokens=50
            )
            
            result = response.choices[0].message.content
            return "successful" in result.lower()
            
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False


class ProcedureLoader:
    """Helper class to load procedures from files or text"""
    
    @staticmethod
    def load_from_files(file_paths: List[str]) -> List[Dict[str, str]]:
        """Load procedures from file paths"""
        procedures = []
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                procedures.append({
                    'name': os.path.basename(file_path),
                    'content': content
                })
                
            except Exception as e:
                print(f"❌ Error loading {file_path}: {e}")
        
        return procedures
    
    @staticmethod
    def load_from_directory(directory_path: str) -> List[Dict[str, str]]:
        """Load all SQL procedures from a directory"""
        import glob
        
        sql_extensions = ['*.sql', '*.hdbprocedure', '*.txt']
        procedures = []
        
        for extension in sql_extensions:
            pattern = os.path.join(directory_path, extension)
            files = glob.glob(pattern)
            
            for file_path in files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    procedures.append({
                        'name': os.path.basename(file_path),
                        'content': content
                    })
                    
                except Exception as e:
                    print(f"❌ Error loading {file_path}: {e}")
        
        return procedures
    
    @staticmethod
    def create_from_text(name: str, content: str) -> Dict[str, str]:
        """Create a procedure from text"""
        return {
            'name': name,
            'content': content
        }
