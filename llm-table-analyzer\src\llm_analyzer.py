"""
LLM-Based SAP HANA Table Analyzer
=================================

This module uses GitHub Models endpoint to analyze SAP HANA SQL procedures
and generate detailed table usage metadata without using ANTLR parser.
"""

import os
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from openai import OpenAI


@dataclass
class AnalysisResult:
    """Result of LLM analysis"""
    success: bool
    table_analysis: Optional[List[Dict[str, Any]]] = None
    raw_response: Optional[str] = None
    error: Optional[str] = None
    procedure_count: int = 0
    total_tables: int = 0


class GitHubModelsLLMAnalyzer:
    """LLM-based analyzer using GitHub Models endpoint"""
    
    def __init__(self, token: str = None):
        """Initialize the LLM analyzer"""
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"
        self.token = token or "****************************************"
        
        self.client = OpenAI(
            base_url=self.endpoint,
            api_key=self.token,
        )
        
        print(f"🤖 LLM Analyzer initialized with GitHub Models endpoint")
    
    def analyze_procedures(self, procedures: List[Dict[str, str]]) -> AnalysisResult:
        """Analyze multiple procedures and generate table usage metadata"""
        
        try:
            # Prepare the prompt with all procedures
            prompt = self._create_analysis_prompt(procedures)
            
            # Call the LLM
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent output
                top_p=0.9,
                model=self.model,
                max_tokens=4000
            )
            
            raw_response = response.choices[0].message.content
            
            # Parse the LLM response
            table_analysis = self._parse_llm_response(raw_response)
            
            return AnalysisResult(
                success=True,
                table_analysis=table_analysis,
                raw_response=raw_response,
                procedure_count=len(procedures),
                total_tables=len(table_analysis) if table_analysis else 0
            )
            
        except Exception as e:
            return AnalysisResult(
                success=False,
                error=str(e)
            )
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the LLM"""
        return """You are an expert SAP HANA SQL analyst. Your task is to analyze SAP HANA SQL procedures and generate a detailed table usage analysis.

You must output ONLY a valid JSON array with the following exact structure for each table:

[
  {
    "Table Name": "TABLE_NAME",
    "Used in Procedures": "procedure1, procedure2, procedure3",
    "Procedure Count": 3,
    "All Columns": "TABLE.COLUMN1, TABLE.COLUMN2, TABLE.COLUMN3",
    "Usage Contexts": "FILTER, JOIN, SELECT",
    "Filter Patterns": "COLUMN1: condition1 | COLUMN2: condition2",
    "Join Relationships": "→ TABLE2: join_condition | → TABLE3: join_condition"
  }
]

Rules:
1. Consolidate all tables across ALL procedures (one row per unique table)
2. List ALL procedures that use each table
3. Include ALL columns referenced for each table with table prefix (TABLE.COLUMN)
4. Extract detailed filter conditions in format "column: condition"
5. Extract detailed join relationships in format "→ TARGET_TABLE: condition"
6. Use pipe (|) to separate multiple filter patterns or join relationships
7. Output ONLY valid JSON, no explanations or markdown
8. Focus on SAP standard tables (VBAK, VBAP, VBRK, VBRP, BSEG, BKPF, etc.)"""
    
    def _create_analysis_prompt(self, procedures: List[Dict[str, str]]) -> str:
        """Create the analysis prompt with procedure content"""
        
        prompt = "Analyze the following SAP HANA SQL procedures and generate table usage metadata:\n\n"
        
        for i, proc in enumerate(procedures, 1):
            prompt += f"=== PROCEDURE {i}: {proc['name']} ===\n"
            prompt += f"{proc['content']}\n\n"
        
        prompt += """
Generate a consolidated table analysis with the exact JSON format specified in the system prompt.
Focus on:
1. Table names and their usage across procedures
2. All columns referenced for each table
3. Filter conditions (WHERE clauses)
4. Join relationships between tables
5. Usage contexts (SELECT, INSERT, UPDATE, DELETE, etc.)

Output only the JSON array, no other text."""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse the LLM response to extract table analysis"""
        
        try:
            # Clean the response to extract JSON
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            table_analysis = json.loads(cleaned_response)
            
            # Validate the structure
            if isinstance(table_analysis, list):
                validated_analysis = []
                for item in table_analysis:
                    if isinstance(item, dict) and "Table Name" in item:
                        # Ensure all required fields exist
                        validated_item = {
                            "Table Name": item.get("Table Name", ""),
                            "Used in Procedures": item.get("Used in Procedures", ""),
                            "Procedure Count": item.get("Procedure Count", 0),
                            "All Columns": item.get("All Columns", ""),
                            "Usage Contexts": item.get("Usage Contexts", ""),
                            "Filter Patterns": item.get("Filter Patterns", ""),
                            "Join Relationships": item.get("Join Relationships", "")
                        }
                        validated_analysis.append(validated_item)
                
                return validated_analysis
            
            return []
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"Response: {response[:500]}...")
            return []
        except Exception as e:
            print(f"❌ Error parsing LLM response: {e}")
            return []
    
    def _clean_json_response(self, response: str) -> str:
        """Clean the LLM response to extract valid JSON"""
        
        # Remove markdown code blocks
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)
        
        # Find JSON array boundaries
        start_idx = response.find('[')
        end_idx = response.rfind(']')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        # If no array found, try to find object and wrap in array
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return f"[{response[start_idx:end_idx + 1]}]"
        
        return response
    
    def test_connection(self) -> bool:
        """Test the LLM connection"""
        
        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say 'Connection successful' if you can read this."}
                ],
                temperature=0.1,
                model=self.model,
                max_tokens=50
            )
            
            result = response.choices[0].message.content
            return "successful" in result.lower()
            
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False


class ProcedureLoader:
    """Helper class to load procedures from files or text"""
    
    @staticmethod
    def load_from_files(file_paths: List[str]) -> List[Dict[str, str]]:
        """Load procedures from file paths"""
        procedures = []
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                procedures.append({
                    'name': os.path.basename(file_path),
                    'content': content
                })
                
            except Exception as e:
                print(f"❌ Error loading {file_path}: {e}")
        
        return procedures
    
    @staticmethod
    def load_from_directory(directory_path: str) -> List[Dict[str, str]]:
        """Load all SQL procedures from a directory"""
        import glob
        
        sql_extensions = ['*.sql', '*.hdbprocedure', '*.txt']
        procedures = []
        
        for extension in sql_extensions:
            pattern = os.path.join(directory_path, extension)
            files = glob.glob(pattern)
            
            for file_path in files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    procedures.append({
                        'name': os.path.basename(file_path),
                        'content': content
                    })
                    
                except Exception as e:
                    print(f"❌ Error loading {file_path}: {e}")
        
        return procedures
    
    @staticmethod
    def create_from_text(name: str, content: str) -> Dict[str, str]:
        """Create a procedure from text"""
        return {
            'name': name,
            'content': content
        }
