"""
Demo: Enhanced Intelligent Filter Analysis
==========================================

This demonstrates the enhanced LLM analyzer that understands filter conflicts
and provides intelligent recommendations for schema, views, and filter strategies.
"""

import pandas as pd
import json
from datetime import datetime


def create_enhanced_demo_output():
    """Create demo output showing intelligent filter analysis"""
    
    # Enhanced demo data with intelligent filter analysis
    enhanced_demo_data = [
        {
            "Table Name": "SAP_SCHEMA.VBRK",
            "Object Type": "TABLE",
            "Schema": "SAP_SCHEMA",
            "Used in Procedures": "PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_ALL_BILLING.sql",
            "Procedure Count": 3,
            "All Columns": "VBRK.VBELN, VBRK.FKDAT, VBRK.KUNAG, VBRK.NETWR, VBRK.FKART, VBRK.MANDT",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "MANDT: [PR_BILLING: ='100', PR_INVOICE: ='200', PR_ALL_BILLING: ALL_VALUES] | FKDAT: [PR_BILLING,PR_INVOICE: BETWEEN dates, PR_ALL_BILLING: ALL_VALUES] | FKART: [ALL_PROCS: IN('F2','G2')]",
            "Recommended Filter Strategy": "USE_ALL_VALUES",
            "Filter Conflict Analysis": "PR_BILLING,PR_INVOICE use MANDT filters, PR_ALL_BILLING needs all MANDT values - recommend ALL_VALUES for MANDT. FKDAT has similar conflict - recommend ALL_VALUES",
            "Join Relationships": "→ VBRP: VBRK.MANDT = VBRP.MANDT AND VBRK.VBELN = VBRP.VBELN | → KNA1: VBRK.MANDT = KNA1.MANDT AND VBRK.KUNAG = KNA1.KUNNR"
        },
        {
            "Table Name": "_SYS_BIC.BILLING_VIEW",
            "Object Type": "CALCULATION_VIEW",
            "Schema": "_SYS_BIC",
            "Used in Procedures": "PR_ANALYTICS_REPORT.sql, PR_DASHBOARD_DATA.sql",
            "Procedure Count": 2,
            "All Columns": "BILLING_VIEW.CLIENT, BILLING_VIEW.BILLING_DATE, BILLING_VIEW.AMOUNT, BILLING_VIEW.CUSTOMER_ID",
            "Usage Contexts": "SELECT, FILTER",
            "Filter Patterns": "CLIENT: [PR_ANALYTICS: ='100', PR_DASHBOARD: ALL_VALUES] | BILLING_DATE: [ALL_PROCS: >= current_date - 365]",
            "Recommended Filter Strategy": "USE_ALL_VALUES",
            "Filter Conflict Analysis": "PR_ANALYTICS restricts CLIENT='100', PR_DASHBOARD needs all clients - recommend ALL_VALUES for CLIENT",
            "Join Relationships": "→ CUSTOMER_MASTER: BILLING_VIEW.CUSTOMER_ID = CUSTOMER_MASTER.ID"
        },
        {
            "Table Name": "SAP_SCHEMA.VBAP",
            "Object Type": "TABLE", 
            "Schema": "SAP_SCHEMA",
            "Used in Procedures": "PR_ORDER_ITEMS.sql, PR_MATERIAL_ANALYSIS.sql",
            "Procedure Count": 2,
            "All Columns": "VBAP.VBELN, VBAP.POSNR, VBAP.MATNR, VBAP.KWMENG, VBAP.MANDT",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "MANDT: [ALL_PROCS: ='100'] | MATNR: [PR_ORDER_ITEMS: LIKE 'MAT%', PR_MATERIAL: ALL_VALUES]",
            "Recommended Filter Strategy": "USE_ALL_VALUES",
            "Filter Conflict Analysis": "PR_ORDER_ITEMS filters MATNR with LIKE 'MAT%', PR_MATERIAL needs all MATNR values - recommend ALL_VALUES for MATNR",
            "Join Relationships": "→ VBAK: VBAP.MANDT = VBAK.MANDT AND VBAP.VBELN = VBAK.VBELN | → MARA: VBAP.MATNR = MARA.MATNR"
        },
        {
            "Table Name": "CUSTOM_SCHEMA.ORDER_STATUS_VIEW",
            "Object Type": "CDS_VIEW",
            "Schema": "CUSTOM_SCHEMA", 
            "Used in Procedures": "PR_STATUS_REPORT.sql",
            "Procedure Count": 1,
            "All Columns": "ORDER_STATUS_VIEW.ORDER_ID, ORDER_STATUS_VIEW.STATUS, ORDER_STATUS_VIEW.LAST_UPDATED",
            "Usage Contexts": "SELECT, FILTER",
            "Filter Patterns": "STATUS: [PR_STATUS_REPORT: IN('OPEN','PROCESSING')]",
            "Recommended Filter Strategy": "USE_RESTRICTIVE_FILTERS",
            "Filter Conflict Analysis": "Only one procedure uses this view with consistent STATUS filter - safe to use restrictive filters",
            "Join Relationships": "→ VBAK: ORDER_STATUS_VIEW.ORDER_ID = VBAK.VBELN"
        },
        {
            "Table Name": "SAP_SCHEMA.BSEG",
            "Object Type": "TABLE",
            "Schema": "SAP_SCHEMA",
            "Used in Procedures": "PR_ACCOUNTING_FULL.sql, PR_ACCOUNTING_DELTA.sql, PR_ACCOUNTING_SUMMARY.sql",
            "Procedure Count": 3,
            "All Columns": "BSEG.BUKRS, BSEG.BELNR, BSEG.GJAHR, BSEG.BUZEI, BSEG.MANDT, BSEG.DMBTR",
            "Usage Contexts": "FILTER, JOIN, SELECT",
            "Filter Patterns": "MANDT: [PR_FULL,PR_SUMMARY: ALL_VALUES, PR_DELTA: ='100'] | GJAHR: [PR_FULL: ALL_VALUES, PR_DELTA,PR_SUMMARY: >=2023] | BUKRS: [ALL_PROCS: IN('1000','2000')]",
            "Recommended Filter Strategy": "USE_ALL_VALUES", 
            "Filter Conflict Analysis": "PR_FULL needs all MANDT and GJAHR values, others use filters - recommend ALL_VALUES for MANDT and GJAHR. BUKRS filter consistent across all procedures",
            "Join Relationships": "→ BKPF: BSEG.MANDT = BKPF.MANDT AND BSEG.BUKRS = BKPF.BUKRS AND BSEG.BELNR = BKPF.BELNR AND BSEG.GJAHR = BKPF.GJAHR"
        }
    ]
    
    return enhanced_demo_data


def display_enhanced_demo():
    """Display the enhanced demo with intelligent filter analysis"""
    
    print("🤖 Enhanced LLM Table Analyzer - Intelligent Filter Analysis Demo")
    print("=" * 80)
    print("Shows schema detection, view classification, and intelligent filter conflict resolution")
    print("=" * 80)
    
    # Get enhanced demo data
    demo_data = create_enhanced_demo_output()
    
    # Display detailed analysis for each object
    for i, obj_data in enumerate(demo_data, 1):
        print(f"\n📋 {i}. {obj_data['Table Name']} ({obj_data['Object Type']})")
        print(f"   Schema: {obj_data['Schema']}")
        print(f"   Used in: {obj_data['Used in Procedures']}")
        print(f"   Procedure Count: {obj_data['Procedure Count']}")
        print(f"   All Columns: {obj_data['All Columns']}")
        print(f"   Usage Contexts: {obj_data['Usage Contexts']}")
        
        print(f"\n   🔍 INTELLIGENT FILTER ANALYSIS:")
        print(f"   Filter Patterns: {obj_data['Filter Patterns']}")
        print(f"   Recommended Strategy: {obj_data['Recommended Filter Strategy']}")
        print(f"   Conflict Analysis: {obj_data['Filter Conflict Analysis']}")
        
        if obj_data['Join Relationships']:
            print(f"\n   🔗 Join Relationships: {obj_data['Join Relationships']}")
    
    # Create DataFrame for CSV export
    df = pd.DataFrame(demo_data)
    
    # Save enhanced demo data
    csv_filename = f"enhanced_filter_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(csv_filename, index=False)
    print(f"\n💾 Enhanced demo data saved to: {csv_filename}")
    
    # Statistics
    print(f"\n📈 Enhanced Analysis Summary:")
    print(f"   Total Objects: {len(demo_data)}")
    
    # Object type breakdown
    object_types = {}
    schemas = set()
    filter_strategies = {}
    conflicts = 0
    
    for obj in demo_data:
        obj_type = obj['Object Type']
        object_types[obj_type] = object_types.get(obj_type, 0) + 1
        
        schemas.add(obj['Schema'])
        
        strategy = obj['Recommended Filter Strategy']
        filter_strategies[strategy] = filter_strategies.get(strategy, 0) + 1
        
        if 'conflict' in obj['Filter Conflict Analysis'].lower():
            conflicts += 1
    
    print(f"   Object Types: {dict(object_types)}")
    print(f"   Unique Schemas: {len(schemas)} ({', '.join(schemas)})")
    print(f"   Filter Strategies: {dict(filter_strategies)}")
    print(f"   Objects with Filter Conflicts: {conflicts}")
    
    return demo_data


def show_filter_intelligence_examples():
    """Show specific examples of intelligent filter analysis"""
    
    print("\n" + "=" * 80)
    print("🧠 INTELLIGENT FILTER ANALYSIS EXAMPLES")
    print("=" * 80)
    
    examples = [
        {
            "scenario": "MANDT Filter Conflict",
            "description": "Some procedures filter MANDT='100', others need all MANDT values",
            "procedures": [
                "PR_CLIENT_100: WHERE MANDT = '100'",
                "PR_ALL_CLIENTS: No MANDT filter (needs all)"
            ],
            "llm_analysis": "MANDT: [PR_CLIENT_100: ='100', PR_ALL_CLIENTS: ALL_VALUES]",
            "recommendation": "USE_ALL_VALUES",
            "reasoning": "PR_ALL_CLIENTS needs all MANDT values, so restrict would break it"
        },
        {
            "scenario": "Date Range Conflict", 
            "description": "Different procedures need different date ranges",
            "procedures": [
                "PR_RECENT: WHERE ERDAT >= '2024-01-01'",
                "PR_HISTORICAL: No date filter (needs all dates)"
            ],
            "llm_analysis": "ERDAT: [PR_RECENT: >='2024-01-01', PR_HISTORICAL: ALL_VALUES]",
            "recommendation": "USE_ALL_VALUES",
            "reasoning": "PR_HISTORICAL needs historical data, so date filter would break it"
        },
        {
            "scenario": "Consistent Filter",
            "description": "All procedures use same filter consistently",
            "procedures": [
                "PR_ACTIVE: WHERE STATUS = 'ACTIVE'",
                "PR_REPORT: WHERE STATUS = 'ACTIVE'"
            ],
            "llm_analysis": "STATUS: [ALL_PROCS: ='ACTIVE']",
            "recommendation": "USE_RESTRICTIVE_FILTERS", 
            "reasoning": "All procedures consistently filter STATUS='ACTIVE', safe to restrict"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n🔍 Example {i}: {example['scenario']}")
        print(f"   Description: {example['description']}")
        print(f"   Procedures:")
        for proc in example['procedures']:
            print(f"     - {proc}")
        print(f"   LLM Analysis: {example['llm_analysis']}")
        print(f"   Recommendation: {example['recommendation']}")
        print(f"   Reasoning: {example['reasoning']}")


def show_schema_view_detection():
    """Show schema and view detection capabilities"""
    
    print("\n" + "=" * 80)
    print("🏗️ SCHEMA AND VIEW DETECTION CAPABILITIES")
    print("=" * 80)
    
    detection_examples = [
        {
            "sql_snippet": "FROM _SYS_BIC.\"analytics.views::BILLING_OVERVIEW\"",
            "detected_object": "_SYS_BIC.BILLING_OVERVIEW",
            "object_type": "CALCULATION_VIEW",
            "schema": "_SYS_BIC"
        },
        {
            "sql_snippet": "FROM SAP_SCHEMA.VBAK V",
            "detected_object": "SAP_SCHEMA.VBAK", 
            "object_type": "TABLE",
            "schema": "SAP_SCHEMA"
        },
        {
            "sql_snippet": "FROM CUSTOM_CDS.OrderStatusView",
            "detected_object": "CUSTOM_CDS.OrderStatusView",
            "object_type": "CDS_VIEW", 
            "schema": "CUSTOM_CDS"
        },
        {
            "sql_snippet": "FROM \"_SYS_BIC\".\"mypackage.views/CV_SALES\"",
            "detected_object": "_SYS_BIC.CV_SALES",
            "object_type": "CALCULATION_VIEW",
            "schema": "_SYS_BIC"
        }
    ]
    
    print("The enhanced LLM analyzer can detect:")
    print("✅ Schema names (SAP_SCHEMA, _SYS_BIC, CUSTOM_SCHEMA)")
    print("✅ Object types (TABLE, VIEW, CDS_VIEW, CALCULATION_VIEW)")
    print("✅ Full qualified names (schema.object)")
    print("✅ SAP HANA specific syntax")
    
    print(f"\n📋 Detection Examples:")
    for example in detection_examples:
        print(f"\n   SQL: {example['sql_snippet']}")
        print(f"   Detected: {example['detected_object']}")
        print(f"   Type: {example['object_type']}")
        print(f"   Schema: {example['schema']}")


if __name__ == "__main__":
    demo_data = display_enhanced_demo()
    show_filter_intelligence_examples()
    show_schema_view_detection()
    
    print("\n🎉 Enhanced intelligent filter analysis demo completed!")
    print("The LLM now understands filter conflicts and provides smart recommendations.")
