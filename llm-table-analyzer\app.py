"""
LLM-Based SAP HANA Table Analyzer - Streamlit Web Application
============================================================

A web application that uses GitHub Models LLM endpoint to analyze SAP HANA SQL procedures
and generate detailed table usage metadata without using ANTLR parser.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import json
from datetime import datetime
from typing import List, Dict, Any
import sys
import os
from pathlib import Path

# Add src directory to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from llm_analyzer import GitHubModelsLLMAnaly<PERSON>, ProcedureLoader, AnalysisResult

# Page configuration
st.set_page_config(
    page_title="LLM SAP HANA Table Analyzer",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

class LLMTableAnalyzerApp:
    """Main Streamlit application class for LLM-based table analysis"""
    
    def __init__(self):
        self.analyzer = None
        
        # Initialize session state
        if 'analysis_results' not in st.session_state:
            st.session_state.analysis_results = None
        if 'procedures_data' not in st.session_state:
            st.session_state.procedures_data = []
        if 'llm_token' not in st.session_state:
            st.session_state.llm_token = "****************************************"
    
    def run(self):
        """Main application entry point"""
        
        # Sidebar
        self._render_sidebar()
        
        # Main content
        st.title("🤖 LLM-Based SAP HANA Table Analyzer")
        st.markdown("**Generate detailed table usage metadata using GitHub Models LLM endpoint**")
        
        # Main tabs
        tab1, tab2, tab3, tab4 = st.tabs([
            "📁 Upload & Analyze", 
            "📊 Analysis Results", 
            "📋 Table Analysis",
            "📈 Visualizations"
        ])
        
        with tab1:
            self._render_upload_tab()
        
        with tab2:
            self._render_results_tab()
        
        with tab3:
            self._render_table_analysis_tab()
        
        with tab4:
            self._render_visualizations_tab()
    
    def _render_sidebar(self):
        """Render sidebar with configuration options"""
        
        st.sidebar.header("⚙️ Configuration")
        
        # LLM Configuration
        st.sidebar.subheader("🤖 GitHub Models LLM")
        
        # Token input
        token = st.sidebar.text_input(
            "GitHub Token",
            value=st.session_state.llm_token,
            type="password",
            help="Your GitHub token for accessing the models endpoint"
        )
        st.session_state.llm_token = token
        
        # Model info
        st.sidebar.info("""
        **Endpoint:** https://models.inference.ai.azure.com
        **Model:** gpt-4o
        **Approach:** Pure LLM-based analysis
        """)
        
        # Test connection
        if st.sidebar.button("🔍 Test LLM Connection"):
            with st.sidebar:
                with st.spinner("Testing connection..."):
                    try:
                        analyzer = GitHubModelsLLMAnalyzer(token)
                        if analyzer.test_connection():
                            st.success("✅ LLM connection successful!")
                        else:
                            st.error("❌ LLM connection failed")
                    except Exception as e:
                        st.error(f"❌ Connection error: {e}")
        
        # Analysis Settings
        st.sidebar.subheader("Analysis Settings")
        max_procedures = st.sidebar.number_input(
            "Max Procedures to Analyze",
            min_value=1,
            max_value=20,
            value=10,
            help="Maximum number of procedures to analyze in one batch (LLM has token limits)"
        )
        
        st.session_state.max_procedures = max_procedures
        
        # Download Section
        st.sidebar.subheader("📥 Downloads")
        if st.session_state.analysis_results:
            self._render_download_buttons()
        
        # Statistics
        if st.session_state.analysis_results:
            st.sidebar.subheader("📊 Statistics")
            result = st.session_state.analysis_results
            st.sidebar.metric("Procedures Analyzed", result.procedure_count)
            st.sidebar.metric("Tables Found", result.total_tables)
            st.sidebar.metric("Success", "✅" if result.success else "❌")
    
    def _render_upload_tab(self):
        """Render the upload and analysis tab"""
        
        st.header("📁 Upload SQL Procedures")
        
        # File upload options
        upload_method = st.radio(
            "Choose upload method:",
            ["Upload Files", "Paste SQL Text"],
            horizontal=True
        )
        
        procedures_data = []
        
        if upload_method == "Upload Files":
            uploaded_files = st.file_uploader(
                "Upload SQL procedure files",
                type=['sql', 'hdbprocedure', 'txt'],
                accept_multiple_files=True,
                help="Upload your SAP HANA SQL procedure files"
            )
            
            if uploaded_files:
                st.success(f"📁 {len(uploaded_files)} files uploaded")
                
                for file in uploaded_files:
                    try:
                        content = file.read().decode('utf-8')
                        procedures_data.append({
                            'name': file.name,
                            'content': content
                        })
                    except Exception as e:
                        st.error(f"Error reading {file.name}: {e}")
        
        else:  # Paste SQL Text
            st.subheader("Paste SQL Procedure")
            
            procedure_name = st.text_input(
                "Procedure Name",
                placeholder="e.g., PR_BILLING_PAYMENT_ANALYSIS"
            )
            
            sql_content = st.text_area(
                "SQL Procedure Content",
                height=300,
                placeholder="Paste your SAP HANA SQL procedure here..."
            )
            
            if procedure_name and sql_content:
                procedures_data.append({
                    'name': procedure_name,
                    'content': sql_content
                })
        
        # Analysis section
        if procedures_data:
            st.subheader("🤖 LLM Analysis")
            
            # Show procedure summary
            total_chars = sum(len(p['content']) for p in procedures_data)
            st.info(f"📊 Ready to analyze {len(procedures_data)} procedures ({total_chars:,} characters)")
            
            # Limit check
            max_procs = st.session_state.get('max_procedures', 10)
            if len(procedures_data) > max_procs:
                st.warning(f"⚠️ Too many procedures. Limiting to first {max_procs} procedures.")
                procedures_data = procedures_data[:max_procs]
            
            # Token estimation
            estimated_tokens = total_chars // 4  # Rough estimation
            if estimated_tokens > 3000:
                st.warning(f"⚠️ Large input (~{estimated_tokens} tokens). Consider reducing the number of procedures.")
            
            # Analysis button
            if st.button("🚀 Start LLM Analysis", type="primary"):
                self._run_llm_analysis(procedures_data)
    
    def _run_llm_analysis(self, procedures_data: List[Dict[str, str]]):
        """Run the LLM analysis"""
        
        with st.spinner("🤖 Analyzing procedures with LLM..."):
            
            try:
                # Initialize analyzer
                analyzer = GitHubModelsLLMAnalyzer(st.session_state.llm_token)
                
                # Run analysis
                result = analyzer.analyze_procedures(procedures_data)
                
                # Store results
                st.session_state.analysis_results = result
                st.session_state.procedures_data = procedures_data
                
                if result.success:
                    st.success(f"🎉 Successfully analyzed {result.procedure_count} procedures!")
                    st.success(f"📊 Found {result.total_tables} unique tables")
                    
                    # Show quick preview
                    if result.table_analysis:
                        st.subheader("📋 Quick Preview")
                        preview_df = pd.DataFrame(result.table_analysis[:5])  # Show first 5 tables
                        st.dataframe(preview_df, use_container_width=True)
                
                else:
                    st.error(f"❌ Analysis failed: {result.error}")
                
            except Exception as e:
                st.error(f"❌ Analysis failed: {e}")
                st.exception(e)
    
    def _render_results_tab(self):
        """Render the analysis results tab"""
        
        if not st.session_state.analysis_results:
            st.info("📊 No analysis results yet. Please upload and analyze procedures first.")
            return
        
        st.header("📊 Analysis Results")
        
        result = st.session_state.analysis_results
        
        # Enhanced summary metrics
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            st.metric("Procedures Analyzed", result.procedure_count)

        with col2:
            st.metric("Tables Found", result.total_tables)

        with col3:
            st.metric("Views Found", getattr(result, 'total_views', 0))

        with col4:
            st.metric("Schemas Found", getattr(result, 'total_schemas', 0))

        with col5:
            st.metric("Filter Conflicts", getattr(result, 'filter_conflicts_detected', 0))
        
        # Raw LLM Response
        if result.raw_response:
            with st.expander("🤖 Raw LLM Response", expanded=False):
                st.text_area(
                    "LLM Response",
                    value=result.raw_response,
                    height=300,
                    disabled=True
                )
        
        # Procedures analyzed
        st.subheader("📋 Procedures Analyzed")
        if st.session_state.procedures_data:
            for i, proc in enumerate(st.session_state.procedures_data, 1):
                with st.expander(f"{i}. {proc['name']}", expanded=False):
                    st.code(proc['content'][:1000] + "..." if len(proc['content']) > 1000 else proc['content'])
    
    def _render_table_analysis_tab(self):
        """Render the table analysis tab"""
        
        if not st.session_state.analysis_results or not st.session_state.analysis_results.table_analysis:
            st.info("📋 No table analysis available. Please run analysis first.")
            return
        
        st.header("📋 LLM-Generated Table Analysis")
        
        # Create DataFrame
        df = pd.DataFrame(st.session_state.analysis_results.table_analysis)
        
        if not df.empty:
            st.subheader("📊 Enhanced Table/View Usage Metadata")

            # Show filter conflict summary if available
            if 'Filter Conflict Analysis' in df.columns:
                conflicts = df[df['Filter Conflict Analysis'].str.len() > 0]
                if not conflicts.empty:
                    st.warning(f"⚠️ {len(conflicts)} objects have filter conflicts - check recommendations!")

            # Display the enhanced dataframe
            st.dataframe(
                df,
                use_container_width=True,
                height=500
            )
            
            # Download button
            csv_data = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Table Analysis (CSV)",
                data=csv_data,
                file_name=f"llm_table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
            
            # Enhanced statistics
            st.subheader("📈 Enhanced Analysis Statistics")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Objects", len(df))

            with col2:
                if 'Object Type' in df.columns:
                    tables_count = len(df[df['Object Type'] == 'TABLE'])
                    st.metric("Tables", tables_count)

            with col3:
                if 'Object Type' in df.columns:
                    views_count = len(df[df['Object Type'].str.contains('VIEW', na=False)])
                    st.metric("Views", views_count)

            with col4:
                if 'Schema' in df.columns:
                    unique_schemas = df['Schema'].nunique()
                    st.metric("Unique Schemas", unique_schemas)

            # Filter analysis statistics
            if 'Recommended Filter Strategy' in df.columns:
                st.subheader("🔍 Filter Strategy Analysis")

                col1, col2, col3 = st.columns(3)

                with col1:
                    use_all = len(df[df['Recommended Filter Strategy'].str.contains('ALL_VALUES', na=False)])
                    st.metric("Recommend ALL_VALUES", use_all)

                with col2:
                    use_filters = len(df[df['Recommended Filter Strategy'].str.contains('RESTRICTIVE', na=False)])
                    st.metric("Recommend FILTERS", use_filters)

                with col3:
                    conflicts = len(df[df['Filter Conflict Analysis'].str.len() > 0])
                    st.metric("Filter Conflicts", conflicts)

            # Show objects with enhanced metadata
            if 'Filter Patterns' in df.columns and 'Join Relationships' in df.columns:
                col1, col2 = st.columns(2)
                with col1:
                    objects_with_filters = len(df[df['Filter Patterns'].str.len() > 0])
                    st.metric("Objects with Filters", objects_with_filters)
                with col2:
                    objects_with_joins = len(df[df['Join Relationships'].str.len() > 0])
                    st.metric("Objects with Joins", objects_with_joins)
        
        else:
            st.warning("No table data available for analysis.")
    
    def _render_visualizations_tab(self):
        """Render the visualizations tab"""
        
        if not st.session_state.analysis_results or not st.session_state.analysis_results.table_analysis:
            st.info("📈 No data available for visualization. Please run analysis first.")
            return
        
        st.header("📈 Data Visualizations")
        
        df = pd.DataFrame(st.session_state.analysis_results.table_analysis)
        
        if df.empty or 'Procedure Count' not in df.columns:
            st.warning("No visualization data available.")
            return
        
        # Table usage distribution
        st.subheader("📊 Table Usage Distribution")
        
        fig_usage = px.bar(
            df,
            x='Table Name',
            y='Procedure Count',
            title="Number of Procedures Using Each Table",
            color='Procedure Count',
            color_continuous_scale='viridis'
        )
        fig_usage.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_usage, use_container_width=True)
        
        # Top tables
        st.subheader("🏆 Top Tables by Usage")
        
        top_tables = df.nlargest(10, 'Procedure Count')
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Most Used Tables:**")
            for i, (_, row) in enumerate(top_tables.iterrows(), 1):
                st.write(f"{i}. **{row['Table Name']}** - {row['Procedure Count']} procedures")
        
        with col2:
            # Pie chart of top tables
            fig_pie = px.pie(
                top_tables,
                values='Procedure Count',
                names='Table Name',
                title="Distribution of Top Tables"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
    
    def _render_download_buttons(self):
        """Render download buttons in sidebar"""
        
        result = st.session_state.analysis_results
        
        if result and result.table_analysis:
            # Download as JSON
            json_data = json.dumps(result.table_analysis, indent=2)
            st.sidebar.download_button(
                label="📥 Download JSON",
                data=json_data,
                file_name=f"llm_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
            
            # Download as CSV
            df = pd.DataFrame(result.table_analysis)
            csv_data = df.to_csv(index=False)
            st.sidebar.download_button(
                label="📥 Download CSV",
                data=csv_data,
                file_name=f"llm_table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )


# Main application entry point
def main():
    """Main application entry point"""
    app = LLMTableAnalyzerApp()
    app.run()


if __name__ == "__main__":
    main()
