T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
A_LETTER=8
ADD=9
AFTER=10
AGENT=11
AGGREGATE=12
ALL=13
ALTER=14
ALPHANUM=15
ANALYZE=16
AND=17
ANY=18
ARRAY=19
AS=20
ASC=21
ASSOCIATE=22
AT=23
ATTRIBUTE=24
AUDIT=25
AUTHID=26
AUTO=27
AUTOMATIC=28
AUTONOMOUS_TRANSACTION=29
BATCH=30
BEFORE=31
BEGIN=32
BETWEEN=33
BFILE=34
BINARY_DOUBLE=35
BINARY_FLOAT=36
BINARY_INTEGER=37
BIGINT=38
BLOB=39
BLOCK=40
BODY=41
BOOLEAN=42
BOTH=43
BREADTH=44
BULK=45
BY=46
BYTE=47
C_LETTER=48
CACHE=49
CALL=50
CANONICAL=51
CASCADE=52
CASE=53
CAST=54
CHAR=55
CHAR_CS=56
CHARACTER=57
CHECK=58
CHR=59
CLOB=60
CLOSE=61
CLUSTER=62
COLLECT=63
COLUMNS=64
COMMENT=65
COMMIT=66
COMMITTED=67
COMPATIBILITY=68
COMPILE=69
COMPOUND=70
CONNECT=71
CONNECT_BY_ROOT=72
CONSTANT=73
CONSTRAINT=74
CONSTRAINTS=75
CONSTRUCTOR=76
CONTENT=77
CONTEXT=78
CONTINUE=79
CONVERT=80
CORRUPT_XID=81
CORRUPT_XID_ALL=82
COST=83
COUNT=84
CREATE=85
CROSS=86
CUBE=87
CURRENT=88
CURRENT_USER=89
CURSOR=90
CUSTOMDATUM=91
CYCLE=92
DATA=93
DATABASE=94
DATE=95
DAY=96
DB_ROLE_CHANGE=97
DBTIMEZONE=98
DDL=99
DEBUG=100
DEC=101
DECIMAL=102
DECLARE=103
DECOMPOSE=104
DECREMENT=105
DEFAULT=106
DEFAULTS=107
DEFERRED=108
DEFINER=109
DELETE=110
DEPTH=111
DESC=112
DETERMINISTIC=113
DIMENSION=114
DISABLE=115
DISASSOCIATE=116
DISTINCT=117
DOCUMENT=118
DOUBLE=119
DROP=120
DSINTERVAL_UNCONSTRAINED=121
EACH=122
ELEMENT=123
ELSE=124
ELSIF=125
EMPTY=126
ENABLE=127
ENCODING=128
END=129
ENTITYESCAPING=130
ERRORS=131
ESCAPE=132
EVALNAME=133
EXCEPTION=134
EXCEPTION_INIT=135
EXCEPTIONS=136
EXCLUDE=137
EXCLUSIVE=138
EXECUTE=139
EXISTS=140
EXIT=141
EXPLAIN=142
EXTERNAL=143
EXTRACT=144
FAILURE=145
FALSE=146
FETCH=147
FINAL=148
FIRST=149
FIRST_VALUE=150
FLOAT=151
FOLLOWING=152
FOLLOWS=153
FOR=154
FORALL=155
FORCE=156
FROM=157
FULL=158
FUNCTION=159
GOTO=160
GRANT=161
GROUP=162
GROUPING=163
HASH=164
HAVING=165
HANDLER=166
HIDE=167
HOUR=168
IF=169
IGNORE=170
IMMEDIATE=171
IN=172
INCLUDE=173
INCLUDING=174
INCREMENT=175
INDENT=176
INDEX=177
INDEXED=178
INDICATOR=179
INDICES=180
INFINITE=181
INLINE=182
INNER=183
INOUT=184
INSERT=185
INSTANTIABLE=186
INSTEAD=187
INT=188
INTEGER=189
INTERSECT=190
INTERVAL=191
INTO=192
INVALIDATE=193
INVOKER=194
IS=195
ISOLATION=196
ITERATE=197
JAVA=198
JOIN=199
KEEP=200
LANGUAGE=201
LAST=202
LAST_VALUE=203
LEADING=204
LEFT=205
LEVEL=206
LIBRARY=207
LIKE=208
LIKE2=209
LIKE4=210
LIKEC=211
LIMIT=212
LOCAL=213
LOCK=214
LOCKED=215
LOG=216
LOGOFF=217
LOGON=218
LONG=219
LOOP=220
MAIN=221
MAP=222
MATCHED=223
MAXVALUE=224
MEASURES=225
MEMBER=226
MERGE=227
MINUS=228
MINUTE=229
MINVALUE=230
MLSLABEL=231
MODE=232
MODEL=233
MODIFY=234
MONTH=235
MULTISET=236
NAME=237
NAN=238
NATURAL=239
NATURALN=240
NAV=241
NCHAR=242
NCHAR_CS=243
NCLOB=244
NESTED=245
NEW=246
NO=247
NOAUDIT=248
NOCACHE=249
NOCOPY=250
NOCYCLE=251
NOENTITYESCAPING=252
NOMAXVALUE=253
NOMINVALUE=254
NONE=255
NOORDER=256
NOSCHEMACHECK=257
NOT=258
NOWAIT=259
NULL=260
NULLS=261
NUMBER=262
NUMERIC=263
NVARCHAR=264
OBJECT=265
OF=266
OFF=267
OID=268
OLD=269
ON=270
ONLY=271
OPEN=272
OPTION=273
OR=274
ORADATA=275
ORDER=276
ORDINALITY=277
OSERROR=278
OUT=279
OUTER=280
OVER=281
OVERRIDING=282
PACKAGE=283
PARALLEL_ENABLE=284
PARAMETERS=285
PARENT=286
PARTITION=287
PASSING=288
PATH=289
PERCENT_ROWTYPE=290
PERCENT_TYPE=291
PIPELINED=292
PIVOT=293
PLAN=294
PLS_INTEGER=295
POSITIVE=296
POSITIVEN=297
PRAGMA=298
PRECEDING=299
PRECISION=300
PRESENT=301
PRIOR=302
PROCEDURE=303
RAISE=304
RANGE=305
RAW=306
READ=307
READS=308
REAL=309
RECORD=310
REF=311
REFERENCE=312
REFERENCING=313
REJECT=314
RELIES_ON=315
RENAME=316
REPLACE=317
RESPECT=318
RESTRICT_REFERENCES=319
RESULT=320
RESULT_CACHE=321
RETURN=322
RETURNING=323
REUSE=324
REVERSE=325
REVOKE=326
RIGHT=327
ROLLBACK=328
ROLLUP=329
ROW=330
ROWID=331
ROWS=332
RULES=333
SAMPLE=334
SAVE=335
SAVEPOINT=336
SCHEMA=337
SCHEMACHECK=338
SCN=339
SEARCH=340
SECOND=341
SECONDDATE=342
SECURITY=343
SEED=344
SEGMENT=345
SELECT=346
SELF=347
SEQUENCE=348
SEQUENTIAL=349
SERIALIZABLE=350
SERIALLY_REUSABLE=351
SERVERERROR=352
SESSIONTIMEZONE=353
SET=354
SETS=355
SETTINGS=356
SHARE=357
SHOW=358
SHUTDOWN=359
SIBLINGS=360
SIGNTYPE=361
SIMPLE_INTEGER=362
SINGLE=363
SIZE=364
SKIP_=365
SMALLINT=366
SMALLDECIMAL=367
SNAPSHOT=368
SOME=369
SPECIFICATION=370
SQL=371
SQLDATA=372
SQLERROR=373
SQLEXCEPTION=374
SQLSCRIPT=375
STANDALONE=376
START=377
STARTUP=378
STATEMENT=379
STATEMENT_ID=380
STATIC=381
STATISTICS=382
STRING=383
SUBMULTISET=384
SUBPARTITION=385
SUBSTITUTABLE=386
SUBTYPE=387
SUCCESS=388
SUSPEND=389
TABLE=390
THE=391
THEN=392
TIME=393
TIMESTAMP=394
TIMESTAMP_LTZ_UNCONSTRAINED=395
TIMESTAMP_TZ_UNCONSTRAINED=396
TIMESTAMP_UNCONSTRAINED=397
TIMEZONE_ABBR=398
TIMEZONE_HOUR=399
TIMEZONE_MINUTE=400
TIMEZONE_REGION=401
TINYINT=402
TO=403
TRAILING=404
TRANSACTION=405
TRANSLATE=406
TREAT=407
TRIGGER=408
TRIM=409
TRUE=410
TRUNCATE=411
TYPE=412
UNBOUNDED=413
UNDER=414
UNION=415
UNIQUE=416
UNLIMITED=417
UNPIVOT=418
UNTIL=419
UPDATE=420
UPDATED=421
UPSERT=422
UROWID=423
USE=424
USING=425
VALIDATE=426
VALUE=427
VALUES=428
VARCHAR=429
VARCHAR2=430
VARIABLE=431
VARRAY=432
VARYING=433
VERSION=434
VERSIONS=435
VIEW=436
WAIT=437
WARNING=438
WELLFORMED=439
WHEN=440
WHENEVER=441
WHERE=442
WHILE=443
WITH=444
WITHIN=445
WORK=446
WRITE=447
XML=448
XMLAGG=449
XMLATTRIBUTES=450
XMLCAST=451
XMLCOLATTVAL=452
XMLELEMENT=453
XMLEXISTS=454
XMLFOREST=455
XMLNAMESPACES=456
XMLPARSE=457
XMLPI=458
XMLQUERY=459
XMLROOT=460
XMLSERIALIZE=461
XMLTABLE=462
YEAR=463
YES=464
YMINTERVAL_UNCONSTRAINED=465
ZONE=466
AUTONOMOUS=467
CONDITION=468
ELSEIF=469
EXECUTION=470
OVERVIEW=471
RESIGNAL=472
MESSAGE_TEXT=473
SHORTTEXT=474
SIGNAL=475
SQL_ERROR_CODE=476
SQL_ERROR_MESSAGE=477
SQLWARNING=478
TEXT=479
UNNEST=480
VARBINARY=481
STRING_AGG=482
CORR_SPEARMAN=483
VAR=484
STDDEV_POP=485
VAR_POP=486
STDDEV_SAMP=487
VAR_SAMP=488
PREDICTION=489
PREDICTION_BOUNDS=490
PREDICTION_COST=491
PREDICTION_DETAILS=492
PREDICTION_PROBABILITY=493
PREDICTION_SET=494
CUME_DIST=495
DENSE_RANK=496
LISTAGG=497
PERCENT_RANK=498
PERCENTILE_CONT=499
PERCENTILE_DISC=500
RANK=501
AVG=502
CORR=503
LAG=504
LEAD=505
MAX=506
MEDIAN=507
MIN=508
NTILE=509
RATIO_TO_REPORT=510
ROW_NUMBER=511
SUM=512
VARIANCE=513
REGR_=514
STDDEV=515
VAR_=516
COVAR_=517
NATIONAL_CHAR_STRING_LIT=518
BIT_STRING_LIT=519
HEX_STRING_LIT=520
DOUBLE_PERIOD=521
PERIOD=522
UNSIGNED_INTEGER=523
APPROXIMATE_NUM_LIT=524
CHAR_STRING=525
DELIMITED_ID=526
PERCENT=527
AMPERSAND=528
LEFT_PAREN=529
RIGHT_PAREN=530
DOUBLE_ASTERISK=531
ASTERISK=532
PLUS_SIGN=533
MINUS_SIGN=534
COMMA=535
SOLIDUS=536
AT_SIGN=537
ASSIGN_OP=538
BINDVAR=539
COLON=540
SEMICOLON=541
LESS_THAN_OR_EQUALS_OP=542
LESS_THAN_OP=543
GREATER_THAN_OR_EQUALS_OP=544
NOT_EQUAL_OP=545
CARRET_OPERATOR_PART=546
TILDE_OPERATOR_PART=547
EXCLAMATION_OPERATOR_PART=548
GREATER_THAN_OP=549
CONCATENATION_OP=550
VERTICAL_BAR=551
EQUALS_OP=552
LEFT_BRACKET=553
RIGHT_BRACKET=554
INTRODUCER=555
SPACES=556
SINGLE_LINE_COMMENT=557
MULTI_LINE_COMMENT=558
PROMPT=559
REGULAR_ID=560
'R'=1
'\''=2
'=>'=3
'..'=4
'!='=5
'<>'=6
'::'=7
'.'=522
'%'=527
'&'=528
'('=529
')'=530
'**'=531
'*'=532
'+'=533
'-'=534
','=535
'/'=536
'@'=537
':='=538
':'=540
';'=541
'<='=542
'<'=543
'>='=544
'^'=546
'~'=547
'!'=548
'>'=549
'||'=550
'|'=551
'='=552
'['=553
']'=554
'_'=555
