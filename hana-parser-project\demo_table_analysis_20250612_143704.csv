Table Name,Used in Procedures,Procedure Count,All Columns,Usage Contexts,Filter Patterns,Join Relationships
VBRP,"PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure",5,"VBRP.AUBEL, VBRP.AUPOS, VBRP.MANDT, VBRP.POSNR, VBRP.VBELN","FILTER, JOIN, SELECT",,→ VBRP: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → VBAP: 1=1 AND VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR | → VBFA: 1=1 AND VBFA.MANDT = VBRP.MANDT AND VBFA.VBELN = VBRP.VBELN AND VBFA.POSNN = VBRP.POSNR | → VBRK: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN
VBAP,"PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure",5,"VBAP.ERDAT, VBAP.MANDT, VBAP.POSNR, VBAP.VBELN","FILTER, JOIN, SELECT",,→ VBRP: 1=1 AND VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR | → VBAK: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = 'C' AND VBAP.ERDAT >= :IP_ERDAT | → VBFA: 1=1 AND VBAP.MANDT = VBFA.MANDT AND VBAP.VBELN = VBFA.VBELV AND VBAP.POSNR = VBFA.POSNV
VBRK,"PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure",5,"VBRK.MANDT, VBRK.VBELN","FILTER, JOIN, SELECT",,→ VBRK: 1=1 AND VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY | → VBRP: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → BKPF: 1=1 AND VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY AND BKPF_INV.AWTYP = 'VBRK' | → WO: 1=1 AND WO.INSTID = VBRK.VBELN
BSEG,"PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure",4,,"FILTER, JOIN, SELECT",,"→ BSEG_INV: 1=1 AND BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.BUKRS = SUBSTRING(CDPOS.OBJECTID,4,4) AND BSEG_INV.BELNR = SUBSTRING(CDPOS.OBJECTID,8,10) AND BSEG_INV.GJAHR = SUBSTRING(CDPOS.OBJECTID,18,4) AND BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D' | → BKPF_INV: 1=1 AND BKPF_INV.MANDT = BSEG_INV.MANDT AND BKPF_INV.BUKRS = BSEG_INV.BUKRS AND BKPF_INV.BELNR = BSEG_INV.BELNR AND BKPF_INV.GJAHR = BSEG_INV.GJAHR AND BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D' AND BKPF_INV.AWTYP = 'VBRK'"
VBAK,"PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure",4,"VBAK.CURRENT_UTCTIMESTAMP, VBAK.INVOIVE, VBAK.MANDT, VBAK.VBELN, VBAK.VBTYP","FILTER, JOIN, SELECT",,→ VBAK: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = 'C' | → VBAP: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = 'C' AND VBAP.ERDAT >= :IP_ERDAT
CDPOS,"PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure",3,"CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.TABKEY, CDPOS.TABNAME, CDPOS.VALUE_NEW, CDPOS.VALUE_OLD","FILTER, JOIN, SELECT","FNAME: CDPOS.FNAME IN ('ZFBDT','ZBD1T') | VALUE_NEW: CDPOS.VALUE_NEW <> ''",→ CDPOS: 1=1 AND CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR AND CDPOS.FNAME = 'ZFBDT' AND CDPOS.TABNAME = 'BSEG'
VBFA,"PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure",2,"VBFA.MANDT, VBFA.POSNN, VBFA.POSNV, VBFA.VBELN, VBFA.VBELV, VBFA.VBTYP_N, VBFA.VBTYP_V","FILTER, JOIN, SELECT",VBTYP_N: VBFA.VBTYP_N = '3',→ VBAP: 1=1 AND VBAP.MANDT = VBFA.MANDT AND VBAP.VBELN = VBFA.VBELV AND VBAP.POSNR = VBFA.POSNV | → VBRP: 1=1 AND VBFA.MANDT = VBRP.MANDT AND VBFA.VBELN = VBRP.VBELN AND VBFA.POSNN = VBRP.POSNR | → VBFA: 1=1 AND VBFA.MANDT = VBRK.MANDT AND VBFA.VBELN = VBRK.VBELN AND VBFA.VBTYP_N = '3' AND VBFA.VBTYP_V = 'M'
