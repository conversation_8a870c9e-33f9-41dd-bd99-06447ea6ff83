"""
SAP HANA SQL Procedure Information Extractor
============================================

This module provides the main functionality to extract detailed information
from SAP HANA SQL procedures using ANTLR parser.
"""

import re
import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from collections import defaultdict

# Import ANTLR components
try:
    # Add the parent directory to access ANTLR generated files
    current_dir = Path(__file__).parent.parent.parent
    generated_dir = current_dir / "generated"
    if generated_dir.exists():
        sys.path.insert(0, str(generated_dir))
    
    from antlr4 import *
    from HanaLexer import HanaLexer
    from HanaParser import HanaParser
    from HanaListener import HanaListener
    ANTLR_AVAILABLE = True
    print("✅ ANTLR SAP HANA grammar loaded successfully")
except ImportError as e:
    ANTLR_AVAILABLE = False
    print(f"⚠️ ANTLR not available: {e}")

from .models import (
    ProcedureInfo, Parameter, Table, Column, FilterCondition, 
    JoinCondition, SQLStatement, Variable, ExtractionResult,
    ParameterDirection, JoinType
)


class HanaExtractionListener(HanaListener if ANTLR_AVAILABLE else object):
    """ANTLR Listener for extracting information from SAP HANA procedures"""
    
    def __init__(self):
        self.procedure_info = ProcedureInfo(name="")
        self.current_context = []
        self.table_aliases = {}
        self.current_statement = None
        self.errors = []
        self.warnings = []
    
    def enterCreate_procedure_body(self, ctx):
        """Extract procedure name and basic information"""
        try:
            # Get procedure name
            if hasattr(ctx, 'proc_name') and ctx.proc_name():
                proc_name = ctx.proc_name().getText()
                self.procedure_info.name = proc_name.strip('"').strip("'")
            
            # Extract language
            if hasattr(ctx, 'lang') and ctx.lang():
                lang_text = ctx.lang().getText()
                self.procedure_info.language = lang_text
            
            # Extract security mode
            if hasattr(ctx, 'security_mode') and ctx.security_mode():
                security_text = ctx.security_mode().getText()
                self.procedure_info.security_mode = security_text
                
            # Extract default schema
            if hasattr(ctx, 'default_schema_name') and ctx.default_schema_name():
                schema_text = ctx.default_schema_name().getText()
                self.procedure_info.default_schema = schema_text
                
        except Exception as e:
            self.errors.append(f"Error extracting procedure header: {str(e)}")
    
    def enterParameter(self, ctx):
        """Extract procedure parameters"""
        try:
            if not hasattr(ctx, 'param_name') or not ctx.param_name():
                return
                
            param_name = ctx.param_name().getText().strip()
            
            # Get parameter direction (IN, OUT, INOUT)
            direction = ParameterDirection.IN  # default
            if ctx.IN():
                direction = ParameterDirection.IN
            elif ctx.OUT():
                direction = ParameterDirection.OUT
            elif ctx.INOUT():
                direction = ParameterDirection.INOUT
            
            # Get parameter type
            param_type = "UNKNOWN"
            if hasattr(ctx, 'param_type') and ctx.param_type():
                param_type = ctx.param_type().getText()
            
            # Get default value if present
            default_value = None
            if hasattr(ctx, 'default_value_part') and ctx.default_value_part():
                default_value = ctx.default_value_part().getText()
            
            parameter = Parameter(
                name=param_name,
                data_type=param_type,
                direction=direction,
                default_value=default_value,
                is_table_type='TABLE' in param_type.upper()
            )
            
            self.procedure_info.parameters.append(parameter)
            
        except Exception as e:
            self.errors.append(f"Error extracting parameter: {str(e)}")
    
    def enterProc_variable(self, ctx):
        """Extract declared variables"""
        try:
            if not hasattr(ctx, 'variable_name_list') or not ctx.variable_name_list():
                return
            
            var_names = ctx.variable_name_list().getText().split(',')
            
            # Get variable type
            var_type = "UNKNOWN"
            if hasattr(ctx, 'sql_type') and ctx.sql_type():
                var_type = ctx.sql_type().getText()
            elif hasattr(ctx, 'array_datatype') and ctx.array_datatype():
                var_type = ctx.array_datatype().getText()
            
            # Check if constant
            is_constant = ctx.CONSTANT() is not None
            
            # Get default value
            default_value = None
            if hasattr(ctx, 'proc_default') and ctx.proc_default():
                default_value = ctx.proc_default().getText()
            
            for var_name in var_names:
                variable = Variable(
                    name=var_name.strip(),
                    data_type=var_type,
                    is_constant=is_constant,
                    default_value=default_value
                )
                self.procedure_info.variables.append(variable)
                
        except Exception as e:
            self.errors.append(f"Error extracting variable: {str(e)}")
    
    def enterTable_name(self, ctx):
        """Extract table references"""
        try:
            table_text = ctx.getText().strip('"').strip("'").upper()
            
            # Filter out obvious non-table names
            if (len(table_text) >= 3 and 
                table_text.replace('_', '').isalnum() and
                not table_text.isdigit() and
                table_text not in ['AND', 'OR', 'IN', 'ON', 'AS', 'SET', 'END']):
                
                if table_text not in self.procedure_info.tables:
                    table = Table(name=table_text)
                    self.procedure_info.tables[table_text] = table
                    
        except Exception as e:
            self.errors.append(f"Error extracting table name: {str(e)}")
    
    def enterEveryRule(self, ctx):
        """Extract information from any rule context"""
        try:
            rule_name = type(ctx).__name__
            self.current_context.append(rule_name)
            
            # Extract table.column patterns from any context
            if hasattr(ctx, 'getText'):
                text = ctx.getText()
                self._extract_column_references(text)
                
        except Exception as e:
            self.warnings.append(f"Warning in rule {rule_name}: {str(e)}")
    
    def exitEveryRule(self, ctx):
        """Clean up context when exiting rules"""
        if self.current_context:
            self.current_context.pop()
    
    def _extract_column_references(self, text: str):
        """Extract table.column references from text"""
        try:
            # Pattern for table.column references
            pattern = r'(\w+)\.(\w+)'
            matches = re.findall(pattern, text, re.IGNORECASE)
            
            for table_or_alias, column in matches:
                table_upper = table_or_alias.upper()
                column_upper = column.upper()
                
                # Skip obvious non-table patterns
                if (len(table_upper) >= 2 and len(column_upper) >= 2 and
                    table_upper not in ['AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT'] and
                    column_upper not in ['AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT']):
                    
                    # Add table if not exists
                    if table_upper not in self.procedure_info.tables:
                        table = Table(name=table_upper)
                        self.procedure_info.tables[table_upper] = table
                    
                    # Add column to table
                    self.procedure_info.tables[table_upper].columns.add(column_upper)
                    
        except Exception as e:
            self.warnings.append(f"Error extracting column references: {str(e)}")


class HanaInformationExtractor:
    """Enhanced main class for extracting information from SAP HANA procedures"""

    def __init__(self):
        # Disable ANTLR by default - use enhanced regex approach
        self.use_antlr = False  # ANTLR_AVAILABLE and False  # Force disable for better results
        print(f"🔧 Enhanced HANA Extractor initialized with {'ANTLR' if self.use_antlr else 'Enhanced Regex Parser'}")
        print("📈 Using improved regex-based parsing for better accuracy")
    
    def extract_from_file(self, file_path: str) -> ExtractionResult:
        """Extract information from a SQL file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            return self.extract_from_sql(sql_content)
        except Exception as e:
            result = ExtractionResult(success=False)
            result.add_error(f"Error reading file {file_path}: {str(e)}")
            return result
    
    def extract_from_sql(self, sql_content: str) -> ExtractionResult:
        """Extract information from SQL content"""
        result = ExtractionResult(success=True)
        
        try:
            if self.use_antlr:
                result = self._extract_with_antlr(sql_content)
            else:
                result = self._extract_with_regex(sql_content)
                result.parsing_method = "FALLBACK"
            
            # Post-process the results
            if result.procedure_info:
                result.procedure_info.raw_sql = sql_content
                self._post_process_extraction(result.procedure_info)
            
        except Exception as e:
            result.success = False
            result.add_error(f"Extraction failed: {str(e)}")
        
        return result
    
    def _extract_with_antlr(self, sql_content: str) -> ExtractionResult:
        """Extract using ANTLR parser"""
        result = ExtractionResult(success=True, parsing_method="ANTLR")
        
        try:
            # Create ANTLR input stream
            input_stream = InputStream(sql_content)
            lexer = HanaLexer(input_stream)
            stream = CommonTokenStream(lexer)
            parser = HanaParser(stream)
            
            # Parse the SQL
            tree = parser.compilation_unit()
            
            # Create and use listener
            listener = HanaExtractionListener()
            walker = ParseTreeWalker()
            walker.walk(listener, tree)
            
            # Get results
            result.procedure_info = listener.procedure_info
            result.errors.extend(listener.errors)
            result.warnings.extend(listener.warnings)
            
            if listener.errors:
                result.success = False
            
        except Exception as e:
            result.success = False
            result.add_error(f"ANTLR parsing failed: {str(e)}")
            # Fallback to regex
            result = self._extract_with_regex(sql_content)
            result.parsing_method = "FALLBACK"
        
        return result

    def _extract_with_regex(self, sql_content: str) -> ExtractionResult:
        """Enhanced regex extraction with intelligent analysis"""
        result = ExtractionResult(success=True, parsing_method="ENHANCED_REGEX")
        procedure_info = ProcedureInfo(name="")

        try:
            # Extract procedure name with schema support
            proc_name_match = re.search(r'CREATE\s+PROCEDURE\s+(?:([^.\s]+)\.)?([^\s(]+)', sql_content, re.IGNORECASE)
            if proc_name_match:
                schema = proc_name_match.group(1)
                proc_name = proc_name_match.group(2).strip('"').strip("'")
                procedure_info.name = proc_name
                if schema:
                    procedure_info.default_schema = schema.strip('"').strip("'")

            # Extract parameters with enhanced parsing
            param_match = re.search(r'CREATE\s+PROCEDURE\s+[^\s(]+\s*\((.*?)\)', sql_content, re.IGNORECASE | re.DOTALL)
            if param_match:
                params_text = param_match.group(1)
                procedure_info.parameters = self._parse_parameters_enhanced(params_text)

            # Extract language and other metadata
            lang_match = re.search(r'LANGUAGE\s+(\w+)', sql_content, re.IGNORECASE)
            if lang_match:
                procedure_info.language = lang_match.group(1)

            security_match = re.search(r'SQL\s+SECURITY\s+(\w+)', sql_content, re.IGNORECASE)
            if security_match:
                procedure_info.security_mode = security_match.group(1)

            # Enhanced table and column extraction
            self._extract_tables_enhanced(sql_content, procedure_info)

            # Enhanced filter and join extraction
            self._extract_filters_enhanced(sql_content, procedure_info)
            self._extract_joins_enhanced(sql_content, procedure_info)

            # Extract variables
            self._extract_variables_enhanced(sql_content, procedure_info)

            # Post-process to clean up invalid table names
            print(f"🧹 Before cleanup: {list(procedure_info.tables.keys())}")
            self._cleanup_invalid_tables(procedure_info)
            print(f"🧹 After cleanup: {list(procedure_info.tables.keys())}")

            result.procedure_info = procedure_info

        except Exception as e:
            result.success = False
            result.add_error(f"Enhanced regex extraction failed: {str(e)}")

        return result

    def _parse_parameters_enhanced(self, params_text: str) -> List[Parameter]:
        """Enhanced parameter parsing with better accuracy"""
        parameters = []

        if not params_text.strip():
            return parameters

        # Split by comma, but be careful with nested parentheses and types
        param_parts = self._smart_split_enhanced(params_text, ',')

        for param_part in param_parts:
            param_part = param_part.strip()
            if not param_part:
                continue

            try:
                # Enhanced parameter parsing: [IN|OUT|INOUT] name type [DEFAULT value]
                # Handle complex types like TABLE(column_name TYPE, ...)
                param_match = re.match(
                    r'(IN|OUT|INOUT)?\s*(\w+)\s+(.*?)(?:\s+DEFAULT\s+(.+))?$',
                    param_part,
                    re.IGNORECASE | re.DOTALL
                )

                if param_match:
                    direction_str = (param_match.group(1) or 'IN').upper()
                    param_name = param_match.group(2)
                    param_type = param_match.group(3).strip()
                    default_val = param_match.group(4)

                    # Clean up parameter type
                    param_type = re.sub(r'\s+', ' ', param_type)

                    direction = ParameterDirection.IN
                    if direction_str == 'OUT':
                        direction = ParameterDirection.OUT
                    elif direction_str == 'INOUT':
                        direction = ParameterDirection.INOUT

                    parameter = Parameter(
                        name=param_name,
                        data_type=param_type,
                        direction=direction,
                        default_value=default_val,
                        is_table_type='TABLE' in param_type.upper()
                    )
                    parameters.append(parameter)

            except Exception as e:
                # If parsing fails, create a basic parameter
                param_name = param_part.split()[0] if param_part.split() else "UNKNOWN"
                parameter = Parameter(
                    name=param_name,
                    data_type="UNKNOWN",
                    direction=ParameterDirection.IN
                )
                parameters.append(parameter)

        return parameters

    def _smart_split_enhanced(self, text: str, delimiter: str) -> List[str]:
        """Enhanced smart split that handles nested parentheses and quotes"""
        parts = []
        current_part = ""
        paren_count = 0
        in_quotes = False
        quote_char = None

        i = 0
        while i < len(text):
            char = text[i]

            # Handle quotes
            if char in ('"', "'") and not in_quotes:
                in_quotes = True
                quote_char = char
            elif char == quote_char and in_quotes:
                in_quotes = False
                quote_char = None

            # Handle parentheses (only when not in quotes)
            elif not in_quotes:
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                elif char == delimiter and paren_count == 0:
                    parts.append(current_part.strip())
                    current_part = ""
                    i += 1
                    continue

            current_part += char
            i += 1

        if current_part.strip():
            parts.append(current_part.strip())

        return parts

    def _extract_tables_enhanced(self, sql_content: str, procedure_info: ProcedureInfo):
        """Enhanced table extraction with schema and view support"""

        # More precise patterns that avoid JOIN conditions and other false positives
        table_patterns = [
            # FROM clause - most reliable
            (r'FROM\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{3,30})"?)(?:\s+(?:AS\s+)?(\w+))?(?=\s|$|,|;|\))', 'SELECT'),

            # JOIN clause - but only the table being joined, not the condition
            (r'(?:INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{3,30})"?)(?:\s+(?:AS\s+)?(\w+))?(?=\s+ON)', 'SELECT'),

            # INSERT INTO
            (r'INSERT\s+INTO\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{3,30})"?)(?=\s|$|,|;|\()', 'INSERT'),

            # UPDATE
            (r'UPDATE\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{3,30})"?)(?=\s+SET|\s|$)', 'UPDATE'),

            # TRUNCATE
            (r'TRUNCATE\s+TABLE\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{3,30})"?)(?=\s|$|;)', 'TRUNCATE'),

            # SAP HANA calculation views
            (r'FROM\s+"?_SYS_BIC"?\."?([^"]+)"?(?:\s+(?:AS\s+)?(\w+))?(?=\s|$|,|;|\))', 'SELECT'),
        ]

        table_aliases = {}
        schema_objects = {}

        # Extract tables with schema information
        for pattern_info in table_patterns:
            pattern, usage_context = pattern_info
            matches = re.finditer(pattern, sql_content, re.IGNORECASE)
            for match in matches:
                groups = match.groups()

                if '_SYS_BIC' in pattern:
                    # Handle calculation views
                    schema = "_SYS_BIC"
                    table_name = groups[0].replace('"', '').replace('/', '.').split('.')[-1].upper()
                    alias = groups[1] if len(groups) > 1 and groups[1] else None
                else:
                    # Handle regular tables
                    schema = groups[0] if groups[0] else None
                    table_name = groups[1] if len(groups) > 1 and groups[1] else groups[0]
                    alias = groups[2] if len(groups) > 2 and groups[2] else None

                if table_name:
                    table_name = table_name.upper()

                    # Filter out obvious non-table names
                    if self._is_valid_table_name(table_name):
                        if table_name not in procedure_info.tables:
                            table = Table(
                                name=table_name,
                                alias=alias,
                                schema=schema
                            )
                            table.usage_context.add(usage_context)
                            procedure_info.tables[table_name] = table
                        else:
                            # Update existing table
                            procedure_info.tables[table_name].usage_context.add(usage_context)
                            if alias and not procedure_info.tables[table_name].alias:
                                procedure_info.tables[table_name].alias = alias
                            if schema and not procedure_info.tables[table_name].schema:
                                procedure_info.tables[table_name].schema = schema

                        if alias:
                            table_aliases[alias.upper()] = table_name

                        if schema:
                            schema_objects[table_name] = schema

        # Enhanced column extraction - only extract columns for known tables
        self._extract_columns_enhanced(sql_content, procedure_info, table_aliases)

    def _is_valid_table_name(self, name: str) -> bool:
        """Enhanced check if a name looks like a valid table name"""
        if not name or len(name) < 3:
            return False

        # Comprehensive list of SQL keywords and invalid table names
        invalid_names = {
            'AND', 'OR', 'IN', 'ON', 'AS', 'SET', 'END', 'BEGIN', 'IF', 'THEN', 'ELSE',
            'WHERE', 'FROM', 'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'JOIN', 'INNER',
            'LEFT', 'RIGHT', 'FULL', 'CROSS', 'UNION', 'GROUP', 'ORDER', 'HAVING',
            'CASE', 'WHEN', 'ELSE', 'NULL', 'NOT', 'IS', 'LIKE', 'BETWEEN', 'EXISTS',
            'ALL', 'ANY', 'SOME', 'DISTINCT', 'TOP', 'LIMIT', 'OFFSET', 'WITH',
            'CREATE', 'ALTER', 'DROP', 'TRUNCATE', 'GRANT', 'REVOKE', 'COMMIT',
            'ROLLBACK', 'SAVEPOINT', 'DECLARE', 'PROCEDURE', 'FUNCTION', 'TRIGGER',
            'VIEW', 'INDEX', 'TABLE', 'DATABASE', 'SCHEMA', 'COLUMN', 'CONSTRAINT',
            'PRIMARY', 'FOREIGN', 'KEY', 'UNIQUE', 'CHECK', 'DEFAULT', 'IDENTITY',
            'AUTO_INCREMENT', 'SERIAL', 'SEQUENCE', 'NEXTVAL', 'CURRVAL',
            'DATE', 'TIME', 'TIMESTAMP', 'DATETIME', 'YEAR', 'MONTH', 'DAY',
            'HOUR', 'MINUTE', 'SECOND', 'INTERVAL', 'EXTRACT', 'DATEPART',
            'VARCHAR', 'CHAR', 'TEXT', 'NVARCHAR', 'NCHAR', 'NTEXT', 'BINARY',
            'VARBINARY', 'IMAGE', 'INT', 'INTEGER', 'BIGINT', 'SMALLINT', 'TINYINT',
            'DECIMAL', 'NUMERIC', 'FLOAT', 'REAL', 'DOUBLE', 'MONEY', 'SMALLMONEY',
            'BIT', 'BOOLEAN', 'BOOL', 'UUID', 'GUID', 'XML', 'JSON', 'CLOB', 'BLOB',
            'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'FIRST', 'LAST', 'STDDEV', 'VARIANCE',
            'COALESCE', 'ISNULL', 'NULLIF', 'CAST', 'CONVERT', 'SUBSTRING', 'LENGTH',
            'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'REPLACE', 'CONCAT',
            'ABS', 'CEILING', 'FLOOR', 'ROUND', 'SQRT', 'POWER', 'EXP', 'LOG',
            'SIN', 'COS', 'TAN', 'ASIN', 'ACOS', 'ATAN', 'ATAN2', 'DEGREES', 'RADIANS',
            'CURRENT_DATE', 'CURRENT_TIME', 'CURRENT_TIMESTAMP', 'GETDATE', 'NOW',
            'USER', 'CURRENT_USER', 'SESSION_USER', 'SYSTEM_USER', 'SUSER_NAME',
            'ROW_NUMBER', 'RANK', 'DENSE_RANK', 'NTILE', 'LAG', 'LEAD', 'FIRST_VALUE',
            'LAST_VALUE', 'OVER', 'PARTITION', 'ROWS', 'RANGE', 'UNBOUNDED', 'PRECEDING',
            'FOLLOWING', 'CURRENT', 'ROW', 'WINDOW', 'FRAME'
        }

        if name.upper() in invalid_names:
            return False

        # Common SAP table prefixes and patterns
        sap_prefixes = [
            'VB', 'BS', 'BK', 'CD', 'KN', 'LF', 'MA', 'MK', 'T0', 'USR', 'SW',
            'CDHDR', 'CDPOS', 'BSEG', 'BKPF', 'VBAK', 'VBAP', 'VBRK', 'VBRP',
            'LIKP', 'LIPS', 'VBUK', 'VBUP', 'KONV', 'PRCD', 'MARA', 'MARD',
            'MAKT', 'MARC', 'MBEW', 'EBAN', 'EBKN', 'EKKO', 'EKPO', 'EKET',
            'MSEG', 'MKPF', 'RESB', 'AUFK', 'AFKO', 'AFPO', 'CAUFV', 'JEST',
            'JSTO', 'TJ02T', 'TJ30T', 'QALS', 'QAVE', 'QAMV'
        ]

        # Check if it's a known SAP table pattern
        if any(name.startswith(prefix) for prefix in sap_prefixes):
            return True

        # Check for custom table patterns (starting with Z or Y)
        if name.startswith(('Z', 'Y')) and len(name) >= 4:
            return True

        # Check for view patterns
        if any(pattern in name for pattern in ['_VIEW', 'VIEW_', 'CV_', '_CV']):
            return True

        # General table name validation - must be alphanumeric with underscores
        if (name.replace('_', '').isalnum() and
            not name.isdigit() and
            len(name) >= 3 and
            len(name) <= 30):  # SAP table names are typically <= 30 chars
            return True

        return False

    def _is_valid_column_name(self, name: str) -> bool:
        """Check if a name looks like a valid column name"""
        if not name or len(name) < 2:
            return False

        # SQL keywords that are definitely not column names
        invalid_column_names = {
            'AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT', 'NULL', 'TRUE', 'FALSE',
            'WHERE', 'FROM', 'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'JOIN',
            'INNER', 'LEFT', 'RIGHT', 'FULL', 'CROSS', 'UNION', 'GROUP', 'ORDER',
            'HAVING', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'IF', 'WHILE',
            'FOR', 'LOOP', 'BREAK', 'CONTINUE', 'RETURN', 'DECLARE', 'SET',
            'BEGIN', 'COMMIT', 'ROLLBACK', 'TRANSACTION', 'PROCEDURE', 'FUNCTION',
            'TRIGGER', 'VIEW', 'INDEX', 'TABLE', 'DATABASE', 'SCHEMA', 'COLUMN',
            'CONSTRAINT', 'PRIMARY', 'FOREIGN', 'KEY', 'UNIQUE', 'CHECK', 'DEFAULT',
            'DATE', 'TIME', 'TIMESTAMP', 'DATETIME', 'YEAR', 'MONTH', 'DAY',
            'HOUR', 'MINUTE', 'SECOND', 'CURRENT_DATE', 'CURRENT_TIME', 'GETDATE',
            'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'FIRST', 'LAST', 'TOP', 'DISTINCT',
            'ALL', 'ANY', 'SOME', 'EXISTS', 'BETWEEN', 'LIKE', 'ESCAPE', 'SIMILAR',
            'REGEXP', 'RLIKE', 'GLOB', 'MATCH', 'SOUNDS', 'SOUNDEX'
        }

        if name.upper() in invalid_column_names:
            return False

        # Must be alphanumeric with underscores, reasonable length
        if (name.replace('_', '').isalnum() and
            not name.isdigit() and
            len(name) <= 128):  # SAP column names are typically <= 128 chars
            return True

        return False

    def _get_usage_context(self, pattern: str) -> str:
        """Determine usage context from pattern"""
        pattern_upper = pattern.upper()
        if 'FROM' in pattern_upper or 'JOIN' in pattern_upper:
            return 'SELECT'
        elif 'INSERT' in pattern_upper:
            return 'INSERT'
        elif 'UPDATE' in pattern_upper:
            return 'UPDATE'
        elif 'TRUNCATE' in pattern_upper:
            return 'TRUNCATE'
        else:
            return 'UNKNOWN'

    def _extract_columns_enhanced(self, sql_content: str, procedure_info: ProcedureInfo, table_aliases: dict):
        """Enhanced column extraction with better accuracy"""

        # Only extract columns for tables that we already identified from FROM/JOIN clauses
        # This prevents false positives from random table.column patterns

        known_tables = set(procedure_info.tables.keys())
        known_aliases = set(table_aliases.keys())
        all_known = known_tables.union(known_aliases)

        if not all_known:
            return  # No tables found, skip column extraction

        # Pattern for table.column references - be very specific to avoid false positives
        # Only match actual table.column patterns, not quoted column names with underscores
        column_pattern = r'\b([A-Z][A-Z0-9_]{2,15})\.([A-Z][A-Z0-9_]{1,30})\b'
        all_matches = re.findall(column_pattern, sql_content, re.IGNORECASE)

        # Filter to only include patterns that are actually table.column references
        filtered_matches = []
        for table_or_alias, column in all_matches:
            table_upper = table_or_alias.upper()
            column_upper = column.upper()

            # Skip if this is from a quoted column name with underscore
            quoted_underscore_pattern = f'"{table_upper}_{column_upper}"'
            if quoted_underscore_pattern in sql_content.upper():
                continue

            # Skip if the table part is obviously not a table name
            if not self._is_valid_table_name(table_upper):
                continue

            # Skip if the column part is obviously not a column name
            if not self._is_valid_column_name(column_upper):
                continue

            filtered_matches.append((table_upper, column_upper))

        matches = filtered_matches

        for table_or_alias, column in matches:
            table_upper = table_or_alias.upper()
            column_upper = column.upper()

            # Only process if the table/alias is one we already know about
            if table_upper not in all_known:
                continue

            # Skip if column name is a SQL keyword
            if not self._is_valid_column_name(column_upper):
                continue

            # Resolve alias to actual table name
            actual_table = table_aliases.get(table_upper, table_upper)

            # Add column to the known table
            if actual_table in procedure_info.tables:
                procedure_info.tables[actual_table].columns.add(column_upper)

    def _is_valid_column_reference(self, table: str, column: str) -> bool:
        """Check if table.column looks like a valid reference"""
        # Skip obvious non-column patterns
        invalid_patterns = [
            'AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT', 'SET', 'END', 'BEGIN',
            'IF', 'THEN', 'ELSE', 'WHEN', 'CASE', 'WHERE', 'GROUP', 'ORDER',
            'HAVING', 'UNION', 'SELECT', 'FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT'
        ]

        if table in invalid_patterns or column in invalid_patterns:
            return False

        # Must be reasonable length
        if len(table) < 2 or len(column) < 2:
            return False

        # Must be alphanumeric (with underscores)
        if not (table.replace('_', '').isalnum() and column.replace('_', '').isalnum()):
            return False

        return True

    def _extract_filters_enhanced(self, sql_content: str, procedure_info: ProcedureInfo):
        """Enhanced filter extraction with intelligent analysis"""

        # Extract WHERE clauses with better pattern matching
        where_patterns = [
            r'WHERE\s+(.*?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s+UNION|\s*;|\s*$)',
            r'AND\s+(.*?)(?:\s+AND|\s+OR|\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s+UNION|\s*;|\s*$)',
            r'OR\s+(.*?)(?:\s+AND|\s+OR|\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s+UNION|\s*;|\s*$)'
        ]

        for pattern in where_patterns:
            where_matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.DOTALL)

            for where_match in where_matches:
                where_clause = where_match.group(1).strip()

                # Extract individual conditions with enhanced patterns
                self._parse_filter_conditions(where_clause, procedure_info)

    def _parse_filter_conditions(self, where_clause: str, procedure_info: ProcedureInfo):
        """Parse individual filter conditions from WHERE clause"""

        # Enhanced condition patterns
        condition_patterns = [
            # Standard comparisons: table.column = value
            r'(\w+)\.(\w+)\s*(=|!=|<>|<|>|<=|>=)\s*([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)',

            # IN clauses: table.column IN (values)
            r'(\w+)\.(\w+)\s+IN\s*\((.*?)\)',

            # LIKE clauses: table.column LIKE pattern
            r'(\w+)\.(\w+)\s+LIKE\s+([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)',

            # BETWEEN clauses: table.column BETWEEN value1 AND value2
            r'(\w+)\.(\w+)\s+BETWEEN\s+([^AND]+?)\s+AND\s+([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)',

            # IS NULL/IS NOT NULL
            r'(\w+)\.(\w+)\s+IS\s+(NOT\s+)?NULL',

            # EXISTS clauses
            r'EXISTS\s*\(\s*SELECT.*?FROM\s+(\w+)',
        ]

        for pattern in condition_patterns:
            condition_matches = re.finditer(pattern, where_clause, re.IGNORECASE)

            for cond_match in condition_matches:
                try:
                    self._create_filter_condition(cond_match, pattern, procedure_info)
                except Exception as e:
                    # Skip problematic conditions
                    continue

    def _create_filter_condition(self, match, pattern: str, procedure_info: ProcedureInfo):
        """Create a filter condition from regex match"""

        groups = match.groups()
        table = groups[0].upper()
        column = groups[1].upper()

        # Determine operator and values based on pattern
        if 'IN' in pattern:
            operator = 'IN'
            value_part = groups[2].strip()
            values = self._parse_in_values(value_part)
        elif 'LIKE' in pattern:
            operator = 'LIKE'
            value_part = groups[2].strip()
            values = [value_part.strip("'").strip('"')]
        elif 'BETWEEN' in pattern:
            operator = 'BETWEEN'
            value1 = groups[2].strip()
            value2 = groups[3].strip()
            values = [value1, value2]
        elif 'NULL' in pattern:
            operator = 'IS NULL' if not groups[2] else 'IS NOT NULL'
            values = []
        elif 'EXISTS' in pattern:
            operator = 'EXISTS'
            values = [groups[0]]  # Table in EXISTS clause
            table = "SUBQUERY"
            column = "EXISTS"
        else:
            operator = groups[2].upper()
            value_part = groups[3].strip()
            values = [value_part.strip("'").strip('"')]

        # Clean up values
        values = [v.strip().strip("'").strip('"') for v in values if v.strip()]

        # Check if it's a parameter reference
        is_parameter = any(':' in v for v in values)

        # Create filter condition
        filter_condition = FilterCondition(
            table=table,
            column=column,
            operator=operator,
            values=values,
            condition_text=f"{table}.{column} {operator} {', '.join(values)}",
            is_parameter=is_parameter
        )

        procedure_info.all_filters.append(filter_condition)

    def _parse_in_values(self, in_clause: str) -> List[str]:
        """Parse values from IN clause"""
        values = []

        # Remove outer parentheses if present
        in_clause = in_clause.strip()
        if in_clause.startswith('(') and in_clause.endswith(')'):
            in_clause = in_clause[1:-1]

        # Split by comma, handling quoted values
        parts = self._smart_split_enhanced(in_clause, ',')

        for part in parts:
            part = part.strip().strip("'").strip('"')
            if part:
                values.append(part)

        return values

    def _extract_joins_enhanced(self, sql_content: str, procedure_info: ProcedureInfo):
        """Enhanced join extraction with better accuracy"""

        # Enhanced join pattern to capture more details
        join_pattern = r'(INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN\s+(?:"?([^".\s]+)"?\.)?(?:"?([A-Z][A-Z0-9_]{2,30})"?)(?:\s+(?:AS\s+)?(\w+))?\s+ON\s+(.*?)(?=\s+(?:INNER|LEFT|RIGHT|FULL|CROSS|WHERE|GROUP|ORDER|HAVING|UNION|;|$))'

        join_matches = re.finditer(join_pattern, sql_content, re.IGNORECASE | re.DOTALL)

        for join_match in join_matches:
            try:
                join_type_str = (join_match.group(1) or 'INNER').upper()
                schema = join_match.group(2)
                right_table = join_match.group(3)
                right_alias = join_match.group(4)
                condition = join_match.group(5).strip()

                if not right_table:
                    continue

                right_table = right_table.upper()

                # Determine join type
                join_type = JoinType.INNER
                if join_type_str == 'LEFT':
                    join_type = JoinType.LEFT
                elif join_type_str == 'RIGHT':
                    join_type = JoinType.RIGHT
                elif join_type_str == 'FULL':
                    join_type = JoinType.FULL
                elif join_type_str == 'CROSS':
                    join_type = JoinType.CROSS

                # Extract left table from condition (enhanced)
                left_table = self._extract_left_table_from_condition_enhanced(condition)

                # Extract columns from join condition
                left_columns = self._extract_columns_from_condition_enhanced(condition, left_table)
                right_columns = self._extract_columns_from_condition_enhanced(condition, right_table)

                if left_table and left_table != "UNKNOWN":
                    join_condition = JoinCondition(
                        left_table=left_table,
                        right_table=right_table,
                        join_type=join_type,
                        condition=condition,
                        left_columns=left_columns,
                        right_columns=right_columns
                    )
                    procedure_info.all_joins.append(join_condition)

            except Exception as e:
                # Skip problematic joins
                continue

    def _extract_left_table_from_condition_enhanced(self, condition: str) -> str:
        """Enhanced extraction of left table from join condition"""

        # Look for table.column patterns to identify tables
        table_matches = re.findall(r'(\w+)\.', condition)

        if table_matches:
            # Return the first table found (usually the left table)
            # Filter out obvious non-table names
            for table in table_matches:
                if self._is_valid_table_name(table.upper()):
                    return table.upper()

        return "UNKNOWN"

    def _extract_columns_from_condition_enhanced(self, condition: str, table_name: str) -> List[str]:
        """Enhanced extraction of columns for a specific table from join condition"""
        columns = []

        if not table_name or table_name == "UNKNOWN":
            return columns

        # Pattern to find table.column references for the specific table
        pattern = rf'{re.escape(table_name)}\.(\w+)'
        matches = re.findall(pattern, condition, re.IGNORECASE)

        for match in matches:
            column = match.upper()
            if self._is_valid_column_reference(table_name, column):
                columns.append(column)

        return columns

    def _extract_variables_enhanced(self, sql_content: str, procedure_info: ProcedureInfo):
        """Enhanced variable extraction"""

        # Pattern for DECLARE statements
        declare_patterns = [
            r'DECLARE\s+(CONSTANT\s+)?(\w+)\s+([^;=]+?)(?:\s*:?=\s*([^;]+?))?;',
            r'DECLARE\s+(CONSTANT\s+)?(\w+)\s+([^;=]+?)(?:\s+DEFAULT\s+([^;]+?))?;'
        ]

        for pattern in declare_patterns:
            declare_matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.DOTALL)

            for declare_match in declare_matches:
                try:
                    is_constant = declare_match.group(1) is not None
                    var_name = declare_match.group(2)
                    var_type = declare_match.group(3).strip()
                    default_value = declare_match.group(4)

                    if default_value:
                        default_value = default_value.strip()

                    variable = Variable(
                        name=var_name,
                        data_type=var_type,
                        is_constant=is_constant,
                        default_value=default_value,
                        is_table_variable='TABLE' in var_type.upper()
                    )

                    procedure_info.variables.append(variable)

                except Exception as e:
                    # Skip problematic variable declarations
                    continue

    def _cleanup_invalid_tables(self, procedure_info: ProcedureInfo):
        """Remove obviously invalid table names that slipped through"""

        print("🧹 CLEANUP METHOD CALLED!")

        # List of definitely invalid table names
        definitely_invalid = {
            'AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT', 'NULL', 'TRUE', 'FALSE',
            'WHERE', 'FROM', 'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'JOIN',
            'INNER', 'LEFT', 'RIGHT', 'FULL', 'CROSS', 'UNION', 'GROUP', 'ORDER',
            'HAVING', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'IF', 'WHILE',
            'FOR', 'LOOP', 'BREAK', 'CONTINUE', 'RETURN', 'DECLARE', 'SET',
            'BEGIN', 'COMMIT', 'ROLLBACK', 'TRANSACTION', 'PROCEDURE', 'FUNCTION',
            'DATE', 'TIME', 'TIMESTAMP', 'DATETIME', 'YEAR', 'MONTH', 'DAY',
            'HOUR', 'MINUTE', 'SECOND', 'CURRENT_DATE', 'CURRENT_TIME', 'GETDATE',
            'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'FIRST', 'LAST', 'TOP', 'DISTINCT',
            'ALL', 'ANY', 'SOME', 'EXISTS', 'BETWEEN', 'LIKE', 'ESCAPE', 'SIMILAR',
            'VARCHAR', 'CHAR', 'TEXT', 'INT', 'INTEGER', 'DECIMAL', 'FLOAT', 'DOUBLE'
        }

        # Remove invalid table names
        tables_to_remove = []
        for table_name in procedure_info.tables.keys():
            if table_name.upper() in definitely_invalid:
                tables_to_remove.append(table_name)

        for table_name in tables_to_remove:
            del procedure_info.tables[table_name]
            print(f"🧹 Removed invalid table name: {table_name}")

        # Also clean up filters and joins that reference invalid tables
        procedure_info.all_filters = [
            f for f in procedure_info.all_filters
            if f.table not in definitely_invalid
        ]

        procedure_info.all_joins = [
            j for j in procedure_info.all_joins
            if j.left_table not in definitely_invalid and j.right_table not in definitely_invalid
        ]

    def _parse_parameters_regex(self, params_text: str) -> List[Parameter]:
        """Parse parameters using regex"""
        parameters = []

        # Split by comma, but be careful with nested parentheses
        param_parts = self._smart_split(params_text, ',')

        for param_part in param_parts:
            param_part = param_part.strip()
            if not param_part:
                continue

            # Parse parameter: [IN|OUT|INOUT] name type [DEFAULT value]
            param_match = re.match(r'(IN|OUT|INOUT)?\s*(\w+)\s+([^,]+?)(?:\s+DEFAULT\s+(.+))?$', param_part, re.IGNORECASE)
            if param_match:
                direction_str = param_match.group(1) or 'IN'
                param_name = param_match.group(2)
                param_type = param_match.group(3).strip()
                default_val = param_match.group(4)

                direction = ParameterDirection.IN
                if direction_str.upper() == 'OUT':
                    direction = ParameterDirection.OUT
                elif direction_str.upper() == 'INOUT':
                    direction = ParameterDirection.INOUT

                parameter = Parameter(
                    name=param_name,
                    data_type=param_type,
                    direction=direction,
                    default_value=default_val,
                    is_table_type='TABLE' in param_type.upper()
                )
                parameters.append(parameter)

        return parameters

    def _extract_tables_regex(self, sql_content: str, procedure_info: ProcedureInfo):
        """Extract tables using regex patterns"""
        # Extract from FROM/JOIN clauses
        table_patterns = [
            r'FROM\s+([A-Z][A-Z0-9_]{2,15})(?:\s+(?:AS\s+)?(\w+))?',
            r'JOIN\s+([A-Z][A-Z0-9_]{2,15})(?:\s+(?:AS\s+)?(\w+))?',
            r'INSERT\s+INTO\s+([A-Z][A-Z0-9_]{2,15})',
            r'UPDATE\s+([A-Z][A-Z0-9_]{2,15})',
            r'TRUNCATE\s+TABLE\s+([A-Z][A-Z0-9_]{2,15})'
        ]

        table_aliases = {}

        for pattern in table_patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE)
            for match in matches:
                table_name = match.group(1).upper()
                alias = match.group(2) if len(match.groups()) > 1 and match.group(2) else None

                if table_name not in procedure_info.tables:
                    table = Table(name=table_name, alias=alias)
                    procedure_info.tables[table_name] = table

                if alias:
                    table_aliases[alias.upper()] = table_name

        # Extract table.column patterns
        column_pattern = r'(\w+)\.(\w+)'
        matches = re.findall(column_pattern, sql_content, re.IGNORECASE)

        for table_or_alias, column in matches:
            table_upper = table_or_alias.upper()
            column_upper = column.upper()

            # Resolve alias to actual table name
            actual_table = table_aliases.get(table_upper, table_upper)

            # Add table if it looks like a valid table name
            if (len(actual_table) >= 3 and
                actual_table.replace('_', '').isalnum() and
                actual_table not in ['AND', 'OR', 'IN', 'ON', 'AS', 'SET', 'END']):

                if actual_table not in procedure_info.tables:
                    table = Table(name=actual_table)
                    procedure_info.tables[actual_table] = table

                # Add column
                procedure_info.tables[actual_table].columns.add(column_upper)

    def _extract_filters_regex(self, sql_content: str, procedure_info: ProcedureInfo):
        """Extract filter conditions using enhanced regex"""
        # Extract WHERE clauses with better pattern matching
        where_patterns = [
            r'WHERE\s+(.*?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s*;|\s*$)',
            r'AND\s+(.*?)(?:\s+AND|\s+OR|\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s*;|\s*$)',
            r'OR\s+(.*?)(?:\s+AND|\s+OR|\s+GROUP\s+BY|\s+ORDER\s+BY|\s+HAVING|\s*;|\s*$)'
        ]

        for pattern in where_patterns:
            where_matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.DOTALL)

            for where_match in where_matches:
                where_clause = where_match.group(1).strip()

                # Extract individual conditions with enhanced patterns
                condition_patterns = [
                    r'(\w+)\.(\w+)\s*(=|!=|<>|<|>|<=|>=)\s*([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)',
                    r'(\w+)\.(\w+)\s+IN\s*\((.*?)\)',
                    r'(\w+)\.(\w+)\s+LIKE\s+([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)',
                    r'(\w+)\.(\w+)\s+BETWEEN\s+([^AND^OR^GROUP^ORDER^HAVING]+?)\s+AND\s+([^AND^OR^GROUP^ORDER^HAVING]+?)(?:\s+AND|\s+OR|\s+GROUP|\s+ORDER|\s+HAVING|$)'
                ]

                for cond_pattern in condition_patterns:
                    condition_matches = re.finditer(cond_pattern, where_clause, re.IGNORECASE)

                    for cond_match in condition_matches:
                        table = cond_match.group(1).upper()
                        column = cond_match.group(2).upper()

                        if 'IN' in cond_pattern:
                            operator = 'IN'
                            value_part = cond_match.group(3).strip()
                        elif 'LIKE' in cond_pattern:
                            operator = 'LIKE'
                            value_part = cond_match.group(3).strip()
                        elif 'BETWEEN' in cond_pattern:
                            operator = 'BETWEEN'
                            value_part = f"{cond_match.group(3).strip()} AND {cond_match.group(4).strip()}"
                        else:
                            operator = cond_match.group(3).upper()
                            value_part = cond_match.group(4).strip()

                        # Clean up value part
                        value_part = value_part.rstrip(',').rstrip(';').strip()

                        # Extract values
                        values = self._extract_filter_values(value_part, operator)
                        is_parameter = ':' in value_part

                        filter_condition = FilterCondition(
                            table=table,
                            column=column,
                            operator=operator,
                            values=values,
                            condition_text=f"{table}.{column} {operator} {value_part}",
                            is_parameter=is_parameter
                        )
                        procedure_info.all_filters.append(filter_condition)

    def _extract_filter_values(self, value_part: str, operator: str) -> List[str]:
        """Extract filter values from value part"""
        values = []

        if operator.upper() == 'IN':
            # Extract values from IN clause
            in_values = re.findall(r"'([^']*)'|(\w+)", value_part)
            for match in in_values:
                value = match[0] if match[0] else match[1]
                if value:
                    values.append(value)
        elif operator.upper() == 'BETWEEN':
            # Extract BETWEEN values
            between_parts = value_part.split(' AND ')
            for part in between_parts:
                clean_part = part.strip().strip("'").strip('"')
                if clean_part:
                    values.append(clean_part)
        else:
            # Single value
            clean_value = value_part.strip().strip("'").strip('"')
            if clean_value:
                values.append(clean_value)

        return values

    def _extract_joins_regex(self, sql_content: str, procedure_info: ProcedureInfo):
        """Extract join conditions using enhanced regex"""
        # Enhanced join pattern to capture more details
        join_pattern = r'(INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN\s+(\w+)(?:\s+(?:AS\s+)?(\w+))?\s+ON\s+(.*?)(?=\s+(?:INNER|LEFT|RIGHT|FULL|CROSS|WHERE|GROUP|ORDER|HAVING|;|$))'

        join_matches = re.finditer(join_pattern, sql_content, re.IGNORECASE | re.DOTALL)

        for join_match in join_matches:
            join_type_str = (join_match.group(1) or 'INNER').upper()
            right_table = join_match.group(2).upper()
            right_alias = join_match.group(3)
            condition = join_match.group(4).strip()

            # Determine join type
            join_type = JoinType.INNER
            if join_type_str == 'LEFT':
                join_type = JoinType.LEFT
            elif join_type_str == 'RIGHT':
                join_type = JoinType.RIGHT
            elif join_type_str == 'FULL':
                join_type = JoinType.FULL
            elif join_type_str == 'CROSS':
                join_type = JoinType.CROSS

            # Extract left table from condition (enhanced)
            left_table = self._extract_left_table_from_condition(condition)

            # Extract columns from join condition
            left_columns = self._extract_columns_from_condition(condition, left_table)
            right_columns = self._extract_columns_from_condition(condition, right_table)

            if left_table:
                join_condition = JoinCondition(
                    left_table=left_table,
                    right_table=right_table,
                    join_type=join_type,
                    condition=condition,
                    left_columns=left_columns,
                    right_columns=right_columns
                )
                procedure_info.all_joins.append(join_condition)

    def _extract_left_table_from_condition(self, condition: str) -> str:
        """Extract left table from join condition"""
        # Look for table.column patterns to identify left table
        table_matches = re.findall(r'(\w+)\.', condition)

        if table_matches:
            # Return the first table found (usually the left table)
            return table_matches[0].upper()

        return "UNKNOWN"

    def _extract_columns_from_condition(self, condition: str, table_name: str) -> List[str]:
        """Extract columns for a specific table from join condition"""
        columns = []

        # Pattern to find table.column references for the specific table
        pattern = rf'{re.escape(table_name)}\.(\w+)'
        matches = re.findall(pattern, condition, re.IGNORECASE)

        for match in matches:
            columns.append(match.upper())

        return columns

    def _smart_split(self, text: str, delimiter: str) -> List[str]:
        """Split text by delimiter, respecting parentheses"""
        parts = []
        current_part = ""
        paren_count = 0

        for char in text:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == delimiter and paren_count == 0:
                parts.append(current_part)
                current_part = ""
                continue

            current_part += char

        if current_part:
            parts.append(current_part)

        return parts

    def _post_process_extraction(self, procedure_info: ProcedureInfo):
        """Post-process extracted information"""
        # Set usage context for tables based on SQL patterns
        sql_upper = procedure_info.raw_sql.upper()

        for table_name, table in procedure_info.tables.items():
            if f'FROM {table_name}' in sql_upper or (table.alias and f'FROM {table.alias}' in sql_upper):
                table.usage_context.add('SELECT')
            if f'INSERT INTO {table_name}' in sql_upper:
                table.usage_context.add('INSERT')
            if f'UPDATE {table_name}' in sql_upper:
                table.usage_context.add('UPDATE')
            if f'TRUNCATE TABLE {table_name}' in sql_upper:
                table.usage_context.add('TRUNCATE')




