"""
SAP HANA SQL Procedure Information Extractor
============================================

This module provides the main functionality to extract detailed information
from SAP HANA SQL procedures using ANTLR parser.
"""

import re
import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from collections import defaultdict

# Import ANTLR components
try:
    # Add the parent directory to access ANTLR generated files
    current_dir = Path(__file__).parent.parent.parent
    generated_dir = current_dir / "generated"
    if generated_dir.exists():
        sys.path.insert(0, str(generated_dir))
    
    from antlr4 import *
    from HanaLexer import HanaLexer
    from HanaParser import HanaParser
    from HanaListener import HanaListener
    ANTLR_AVAILABLE = True
    print("✅ ANTLR SAP HANA grammar loaded successfully")
except ImportError as e:
    ANTLR_AVAILABLE = False
    print(f"⚠️ ANTLR not available: {e}")

from .models import (
    ProcedureInfo, Parameter, Table, Column, FilterCondition, 
    JoinCondition, SQLStatement, Variable, ExtractionResult,
    ParameterDirection, JoinType
)


class HanaExtractionListener(HanaListener if ANTLR_AVAILABLE else object):
    """ANTLR Listener for extracting information from SAP HANA procedures"""
    
    def __init__(self):
        self.procedure_info = ProcedureInfo(name="")
        self.current_context = []
        self.table_aliases = {}
        self.current_statement = None
        self.errors = []
        self.warnings = []
    
    def enterCreate_procedure_body(self, ctx):
        """Extract procedure name and basic information"""
        try:
            # Get procedure name
            if hasattr(ctx, 'proc_name') and ctx.proc_name():
                proc_name = ctx.proc_name().getText()
                self.procedure_info.name = proc_name.strip('"').strip("'")
            
            # Extract language
            if hasattr(ctx, 'lang') and ctx.lang():
                lang_text = ctx.lang().getText()
                self.procedure_info.language = lang_text
            
            # Extract security mode
            if hasattr(ctx, 'security_mode') and ctx.security_mode():
                security_text = ctx.security_mode().getText()
                self.procedure_info.security_mode = security_text
                
            # Extract default schema
            if hasattr(ctx, 'default_schema_name') and ctx.default_schema_name():
                schema_text = ctx.default_schema_name().getText()
                self.procedure_info.default_schema = schema_text
                
        except Exception as e:
            self.errors.append(f"Error extracting procedure header: {str(e)}")
    
    def enterParameter(self, ctx):
        """Extract procedure parameters"""
        try:
            if not hasattr(ctx, 'param_name') or not ctx.param_name():
                return
                
            param_name = ctx.param_name().getText().strip()
            
            # Get parameter direction (IN, OUT, INOUT)
            direction = ParameterDirection.IN  # default
            if ctx.IN():
                direction = ParameterDirection.IN
            elif ctx.OUT():
                direction = ParameterDirection.OUT
            elif ctx.INOUT():
                direction = ParameterDirection.INOUT
            
            # Get parameter type
            param_type = "UNKNOWN"
            if hasattr(ctx, 'param_type') and ctx.param_type():
                param_type = ctx.param_type().getText()
            
            # Get default value if present
            default_value = None
            if hasattr(ctx, 'default_value_part') and ctx.default_value_part():
                default_value = ctx.default_value_part().getText()
            
            parameter = Parameter(
                name=param_name,
                data_type=param_type,
                direction=direction,
                default_value=default_value,
                is_table_type='TABLE' in param_type.upper()
            )
            
            self.procedure_info.parameters.append(parameter)
            
        except Exception as e:
            self.errors.append(f"Error extracting parameter: {str(e)}")
    
    def enterProc_variable(self, ctx):
        """Extract declared variables"""
        try:
            if not hasattr(ctx, 'variable_name_list') or not ctx.variable_name_list():
                return
            
            var_names = ctx.variable_name_list().getText().split(',')
            
            # Get variable type
            var_type = "UNKNOWN"
            if hasattr(ctx, 'sql_type') and ctx.sql_type():
                var_type = ctx.sql_type().getText()
            elif hasattr(ctx, 'array_datatype') and ctx.array_datatype():
                var_type = ctx.array_datatype().getText()
            
            # Check if constant
            is_constant = ctx.CONSTANT() is not None
            
            # Get default value
            default_value = None
            if hasattr(ctx, 'proc_default') and ctx.proc_default():
                default_value = ctx.proc_default().getText()
            
            for var_name in var_names:
                variable = Variable(
                    name=var_name.strip(),
                    data_type=var_type,
                    is_constant=is_constant,
                    default_value=default_value
                )
                self.procedure_info.variables.append(variable)
                
        except Exception as e:
            self.errors.append(f"Error extracting variable: {str(e)}")
    
    def enterTable_name(self, ctx):
        """Extract table references"""
        try:
            table_text = ctx.getText().strip('"').strip("'").upper()
            
            # Filter out obvious non-table names
            if (len(table_text) >= 3 and 
                table_text.replace('_', '').isalnum() and
                not table_text.isdigit() and
                table_text not in ['AND', 'OR', 'IN', 'ON', 'AS', 'SET', 'END']):
                
                if table_text not in self.procedure_info.tables:
                    table = Table(name=table_text)
                    self.procedure_info.tables[table_text] = table
                    
        except Exception as e:
            self.errors.append(f"Error extracting table name: {str(e)}")
    
    def enterEveryRule(self, ctx):
        """Extract information from any rule context"""
        try:
            rule_name = type(ctx).__name__
            self.current_context.append(rule_name)
            
            # Extract table.column patterns from any context
            if hasattr(ctx, 'getText'):
                text = ctx.getText()
                self._extract_column_references(text)
                
        except Exception as e:
            self.warnings.append(f"Warning in rule {rule_name}: {str(e)}")
    
    def exitEveryRule(self, ctx):
        """Clean up context when exiting rules"""
        if self.current_context:
            self.current_context.pop()
    
    def _extract_column_references(self, text: str):
        """Extract table.column references from text"""
        try:
            # Pattern for table.column references
            pattern = r'(\w+)\.(\w+)'
            matches = re.findall(pattern, text, re.IGNORECASE)
            
            for table_or_alias, column in matches:
                table_upper = table_or_alias.upper()
                column_upper = column.upper()
                
                # Skip obvious non-table patterns
                if (len(table_upper) >= 2 and len(column_upper) >= 2 and
                    table_upper not in ['AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT'] and
                    column_upper not in ['AND', 'OR', 'IN', 'ON', 'AS', 'IS', 'NOT']):
                    
                    # Add table if not exists
                    if table_upper not in self.procedure_info.tables:
                        table = Table(name=table_upper)
                        self.procedure_info.tables[table_upper] = table
                    
                    # Add column to table
                    self.procedure_info.tables[table_upper].columns.add(column_upper)
                    
        except Exception as e:
            self.warnings.append(f"Error extracting column references: {str(e)}")


class HanaInformationExtractor:
    """Main class for extracting information from SAP HANA procedures"""
    
    def __init__(self):
        self.use_antlr = ANTLR_AVAILABLE
        print(f"🔧 HANA Extractor initialized with {'ANTLR' if self.use_antlr else 'Regex fallback'}")
    
    def extract_from_file(self, file_path: str) -> ExtractionResult:
        """Extract information from a SQL file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            return self.extract_from_sql(sql_content)
        except Exception as e:
            result = ExtractionResult(success=False)
            result.add_error(f"Error reading file {file_path}: {str(e)}")
            return result
    
    def extract_from_sql(self, sql_content: str) -> ExtractionResult:
        """Extract information from SQL content"""
        result = ExtractionResult(success=True)
        
        try:
            if self.use_antlr:
                result = self._extract_with_antlr(sql_content)
            else:
                result = self._extract_with_regex(sql_content)
                result.parsing_method = "FALLBACK"
            
            # Post-process the results
            if result.procedure_info:
                result.procedure_info.raw_sql = sql_content
                self._post_process_extraction(result.procedure_info)
            
        except Exception as e:
            result.success = False
            result.add_error(f"Extraction failed: {str(e)}")
        
        return result
    
    def _extract_with_antlr(self, sql_content: str) -> ExtractionResult:
        """Extract using ANTLR parser"""
        result = ExtractionResult(success=True, parsing_method="ANTLR")
        
        try:
            # Create ANTLR input stream
            input_stream = InputStream(sql_content)
            lexer = HanaLexer(input_stream)
            stream = CommonTokenStream(lexer)
            parser = HanaParser(stream)
            
            # Parse the SQL
            tree = parser.compilation_unit()
            
            # Create and use listener
            listener = HanaExtractionListener()
            walker = ParseTreeWalker()
            walker.walk(listener, tree)
            
            # Get results
            result.procedure_info = listener.procedure_info
            result.errors.extend(listener.errors)
            result.warnings.extend(listener.warnings)
            
            if listener.errors:
                result.success = False
            
        except Exception as e:
            result.success = False
            result.add_error(f"ANTLR parsing failed: {str(e)}")
            # Fallback to regex
            result = self._extract_with_regex(sql_content)
            result.parsing_method = "FALLBACK"
        
        return result

    def _extract_with_regex(self, sql_content: str) -> ExtractionResult:
        """Extract using regex patterns as fallback"""
        result = ExtractionResult(success=True, parsing_method="FALLBACK")
        procedure_info = ProcedureInfo(name="")

        try:
            # Extract procedure name
            proc_name_match = re.search(r'CREATE\s+PROCEDURE\s+([^\s(]+)', sql_content, re.IGNORECASE)
            if proc_name_match:
                procedure_info.name = proc_name_match.group(1).strip('"').strip("'")

            # Extract parameters
            param_match = re.search(r'CREATE\s+PROCEDURE\s+[^\s(]+\s*\((.*?)\)', sql_content, re.IGNORECASE | re.DOTALL)
            if param_match:
                params_text = param_match.group(1)
                procedure_info.parameters = self._parse_parameters_regex(params_text)

            # Extract language
            lang_match = re.search(r'LANGUAGE\s+(\w+)', sql_content, re.IGNORECASE)
            if lang_match:
                procedure_info.language = lang_match.group(1)

            # Extract tables and columns
            self._extract_tables_regex(sql_content, procedure_info)

            # Extract filters and joins
            self._extract_filters_regex(sql_content, procedure_info)
            self._extract_joins_regex(sql_content, procedure_info)

            result.procedure_info = procedure_info

        except Exception as e:
            result.success = False
            result.add_error(f"Regex extraction failed: {str(e)}")

        return result

    def _parse_parameters_regex(self, params_text: str) -> List[Parameter]:
        """Parse parameters using regex"""
        parameters = []

        # Split by comma, but be careful with nested parentheses
        param_parts = self._smart_split(params_text, ',')

        for param_part in param_parts:
            param_part = param_part.strip()
            if not param_part:
                continue

            # Parse parameter: [IN|OUT|INOUT] name type [DEFAULT value]
            param_match = re.match(r'(IN|OUT|INOUT)?\s*(\w+)\s+([^,]+?)(?:\s+DEFAULT\s+(.+))?$', param_part, re.IGNORECASE)
            if param_match:
                direction_str = param_match.group(1) or 'IN'
                param_name = param_match.group(2)
                param_type = param_match.group(3).strip()
                default_val = param_match.group(4)

                direction = ParameterDirection.IN
                if direction_str.upper() == 'OUT':
                    direction = ParameterDirection.OUT
                elif direction_str.upper() == 'INOUT':
                    direction = ParameterDirection.INOUT

                parameter = Parameter(
                    name=param_name,
                    data_type=param_type,
                    direction=direction,
                    default_value=default_val,
                    is_table_type='TABLE' in param_type.upper()
                )
                parameters.append(parameter)

        return parameters

    def _extract_tables_regex(self, sql_content: str, procedure_info: ProcedureInfo):
        """Extract tables using regex patterns"""
        # Extract from FROM/JOIN clauses
        table_patterns = [
            r'FROM\s+([A-Z][A-Z0-9_]{2,15})(?:\s+(?:AS\s+)?(\w+))?',
            r'JOIN\s+([A-Z][A-Z0-9_]{2,15})(?:\s+(?:AS\s+)?(\w+))?',
            r'INSERT\s+INTO\s+([A-Z][A-Z0-9_]{2,15})',
            r'UPDATE\s+([A-Z][A-Z0-9_]{2,15})',
            r'TRUNCATE\s+TABLE\s+([A-Z][A-Z0-9_]{2,15})'
        ]

        table_aliases = {}

        for pattern in table_patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE)
            for match in matches:
                table_name = match.group(1).upper()
                alias = match.group(2) if len(match.groups()) > 1 and match.group(2) else None

                if table_name not in procedure_info.tables:
                    table = Table(name=table_name, alias=alias)
                    procedure_info.tables[table_name] = table

                if alias:
                    table_aliases[alias.upper()] = table_name

        # Extract table.column patterns
        column_pattern = r'(\w+)\.(\w+)'
        matches = re.findall(column_pattern, sql_content, re.IGNORECASE)

        for table_or_alias, column in matches:
            table_upper = table_or_alias.upper()
            column_upper = column.upper()

            # Resolve alias to actual table name
            actual_table = table_aliases.get(table_upper, table_upper)

            # Add table if it looks like a valid table name
            if (len(actual_table) >= 3 and
                actual_table.replace('_', '').isalnum() and
                actual_table not in ['AND', 'OR', 'IN', 'ON', 'AS', 'SET', 'END']):

                if actual_table not in procedure_info.tables:
                    table = Table(name=actual_table)
                    procedure_info.tables[actual_table] = table

                # Add column
                procedure_info.tables[actual_table].columns.add(column_upper)
