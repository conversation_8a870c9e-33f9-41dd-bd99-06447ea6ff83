Table Name,Used in Procedures,Procedure Count,All Columns,Usage Contexts,Filter Patterns,Join Relationships
VBRK,"PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_REVENUE_ANALYSIS.sql",3,"VBRK.VBELN, VBRK.FKDAT, VBRK.KUNAG, VBRK.NETWR, VBRK.FKART, VBRK.MANDT","FILTER, JOIN, SELECT","FKDAT: VBRK.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO | FKART: VBRK.FKART IN ('F2', 'G2') | MANDT: VBRK.MANDT = :IP_CLIENT",→ VBRP: VBRK.MANDT = VBRP.MANDT AND VBRK.VBELN = VBRP.VBELN | → KNA1: VBRK.MANDT = KNA1.MANDT AND VBRK.KUNAG = KNA1.KUNNR
VBRP,"PR_BILLING_ANALYSIS.sql, PR_INVOICE_REPORT.sql, PR_ITEM_ANALYSIS.sql",3,"VBRP.VBELN, VBRP.POSNR, VBRP.MATNR, VBRP.NETWR, VBRP.MANDT, VBRP.AUBEL, VBRP.AUPOS","FILTER, JOIN, SELECT",NETWR: VBRP.NETWR > 0 | MATNR: VBRP.MATNR LIKE 'MAT%',→ VBRK: VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → VBAP: VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR
VBAK,"PR_ORDER_ANALYSIS.sql, PR_SALES_REPORT.sql",2,"VBAK.VBELN, VBAK.ERDAT, VBAK.KUNNR, VBAK.VBTYP, VBAK.MANDT","FILTER, JOIN, SELECT",VBTYP: VBAK.VBTYP = 'C' | ERDAT: VBAK.ERDAT >= :IP_DATE_FROM,→ VBAP: VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN | → KNA1: VBAK.MANDT = KNA1.MANDT AND VBAK.KUNNR = KNA1.KUNNR
VBAP,"PR_ORDER_ANALYSIS.sql, PR_ITEM_DETAILS.sql",2,"VBAP.VBELN, VBAP.POSNR, VBAP.MATNR, VBAP.KWMENG, VBAP.MANDT","FILTER, JOIN, SELECT","MATNR: VBAP.MATNR IN ('MAT001', 'MAT002') | KWMENG: VBAP.KWMENG > 0",→ VBAK: VBAP.MANDT = VBAK.MANDT AND VBAP.VBELN = VBAK.VBELN | → VBRP: VBAP.VBELN = VBRP.AUBEL AND VBAP.POSNR = VBRP.AUPOS
KNA1,PR_CUSTOMER_ANALYSIS.sql,1,"KNA1.KUNNR, KNA1.NAME1, KNA1.LAND1, KNA1.MANDT","JOIN, SELECT",LAND1: KNA1.LAND1 = 'DE',→ VBRK: KNA1.MANDT = VBRK.MANDT AND KNA1.KUNNR = VBRK.KUNAG | → VBAK: KNA1.MANDT = VBAK.MANDT AND KNA1.KUNNR = VBAK.KUNNR
