{"extraction_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 25, "parameter_count": 0, "complexity_score": 63, "parsing_method": "FALLBACK", "tables": ["DUMMY", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV"], "table_details": {"DUMMY": {"columns": [], "usage_context": ["SELECT"], "alias": null, "schema": null}, "KNA1": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "KNB1": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "MARA": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VBAK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "LIKP": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VTTK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VBRK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BKPF": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BSID": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BSAD": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "CDHDR": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "SALES": {"columns": [], "usage_context": ["SELECT"], "alias": "Orders", "schema": null}, "BILLING": {"columns": [], "usage_context": ["SELECT"], "alias": "Documents", "schema": null}, "KONV_DATA": {"columns": [], "usage_context": ["SELECT"], "alias": null, "schema": null}, "VBAP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBUK": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBUP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "LIPS": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VTTP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBRP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "BSEG": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBFA": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "CDPOS": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "KONV": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}}, "parameters": [], "filters": [{"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "TPLST", "operator": "=", "values": [":IP_TPLST"], "condition_text": "T1.TPLST = :IP_TPLST", "is_parameter": true}, {"table": "T1", "column": "BUKRS_VF", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "T1.BUKRS_VF = :IP_BUKRS", "is_parameter": true}, {"table": "T2", "column": "BUKRS", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "T2.BUKRS = :IP_BUKRS", "is_parameter": true}, {"table": "T1", "column": "BLART", "operator": "IN", "values": ["RV", "DR", "DZ"], "condition_text": "T1.BLART IN RV, DR, DZ", "is_parameter": false}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "OBJECTCLAS", "operator": "IN", "values": ["VERKBELEG", "LIEFERUNG", "FAKTURA"], "condition_text": "T1.OBJECTCLAS IN VERKBELEG, LIEFERUNG, FAKTURA", "is_parameter": false}, {"table": "T1", "column": "TCODE", "operator": "LIKE", "values": [":IP_TCODE"], "condition_text": "T1.TCODE LIKE :IP_TCODE", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}], "joins": [], "filter_count": 13, "join_count": 0, "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 33, "parsing_method": "FALLBACK", "tables": ["VBRK", "CT_BILLING_PAYMENT_ANALYSIS", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATISTICS", "PAYMENT"], "table_details": {"VBRK": {"columns": ["FKART", "VBELN", "BUKRS", "KNUMV", "VGBEL", "FKDAT", "ZTERM", "MANDT", "KUNAG"], "usage_context": ["SELECT"], "alias": "INNER", "schema": null}, "CT_BILLING_PAYMENT_ANALYSIS": {"columns": [], "usage_context": ["SELECT", "TRUNCATE", "INSERT"], "alias": null, "schema": null}, "VBRP": {"columns": ["NETWR", "MANDT", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "BSID": {"columns": ["KUNNR", "MANDT", "BELNR", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "BSAD": {"columns": ["KUNNR", "AUGDT", "MANDT", "BELNR", "AUGBL"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "KONV": {"columns": ["KWERT", "MANDT", "KSCHL", "KNUMV"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "CT_PAYMENT_STATISTICS": {"columns": [], "usage_context": ["INSERT"], "alias": null, "schema": null}, "PAYMENT": {"columns": [], "usage_context": ["UPDATE"], "alias": null, "schema": null}}, "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "filters": [{"table": "VBRK", "column": "BUKRS", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "VBRK.BUKRS = :IP_BUKRS", "is_parameter": true}, {"table": "VBRK", "column": "FKART", "operator": "IN", "values": ["F2", "G2", "S1"], "condition_text": "VBRK.FKART IN F2, G2, S1", "is_parameter": false}], "joins": [{"left_table": "VBRK", "right_table": "VBRP", "join_type": "INNER", "condition": "VBRK.MANDT = VBRP.MANDT\n        AND VBRK.VBELN = VBRP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBRK", "right_table": "BSID", "join_type": "LEFT", "condition": "VBRK.MANDT = BSID.MANDT\n        AND VBRK.VBELN = BSID.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "BSID", "right_table": "BSAD", "join_type": "LEFT", "condition": "BSID.MANDT = BSAD.MANDT\n        AND BSID.KUNNR = BSAD.KUNNR\n        AND BSID.BELNR = BSAD.BELNR", "left_columns": ["MANDT", "KUNNR", "BELNR"], "right_columns": ["MANDT", "KUNNR", "BELNR"]}, {"left_table": "VBRK", "right_table": "KONV", "join_type": "LEFT", "condition": "VBRK.MANDT = KONV.MANDT\n        AND VBRK.KNUMV = KONV.KNUMV", "left_columns": ["MANDT", "KNUMV"], "right_columns": ["MANDT", "KNUMV"]}], "filter_count": 2, "join_count": 4, "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 2, "parameter_count": 3, "complexity_score": 7, "parsing_method": "FALLBACK", "tables": ["BSEG", "CT_BSEG_DELTA_ACTIVITIES"], "table_details": {"BSEG": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "CT_BSEG_DELTA_ACTIVITIES": {"columns": [], "usage_context": ["TRUNCATE", "INSERT"], "alias": null, "schema": null}}, "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "filters": [], "joins": [], "filter_count": 0, "join_count": 0, "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 33, "parsing_method": "FALLBACK", "tables": ["VBAK", "CT_ORDER_DELIVERY_ANALYSIS", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATISTICS", "STATISTICS"], "table_details": {"VBAK": {"columns": ["AUART", "VBELN", "VKORG", "MANDT", "ERDAT"], "usage_context": ["SELECT"], "alias": "INNER", "schema": null}, "CT_ORDER_DELIVERY_ANALYSIS": {"columns": [], "usage_context": ["SELECT", "TRUNCATE", "INSERT"], "alias": null, "schema": null}, "VBAP": {"columns": ["VBELN", "MANDT", "MATNR", "NETWR", "POSNR"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "VBUK": {"columns": ["GBSTK", "MANDT", "VBELN", "ABSTK"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "LIPS": {"columns": ["VBELN", "VGPOS", "VGBEL", "MANDT", "LFIMG"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "LIKP": {"columns": ["WBSTK", "MANDT", "WADAT", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "CT_PROCESS_STATISTICS": {"columns": [], "usage_context": ["INSERT"], "alias": null, "schema": null}, "STATISTICS": {"columns": [], "usage_context": ["UPDATE"], "alias": null, "schema": null}}, "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "filters": [{"table": "VBAK", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "VBAK.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "VBAK", "column": "AUART", "operator": "IN", "values": ["OR", "TA", "ZOR"], "condition_text": "VBAK.AUART IN OR, TA, ZOR", "is_parameter": false}], "joins": [{"left_table": "VBAK", "right_table": "VBAP", "join_type": "INNER", "condition": "VBAK.MANDT = VBAP.MANDT\n        AND VBAK.VBELN = VBAP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBAK", "right_table": "VBUK", "join_type": "INNER", "condition": "VBAK.MANDT = VBUK.MANDT\n        AND VBAK.VBELN = VBUK.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBAP", "right_table": "LIPS", "join_type": "LEFT", "condition": "VBAP.MANDT = LIPS.MANDT\n        AND VBAP.VBELN = LIPS.VGBEL\n        AND VBAP.POSNR = LIPS.VGPOS", "left_columns": ["MANDT", "VBELN", "POSNR"], "right_columns": ["MANDT", "VGBEL", "VGPOS"]}, {"left_table": "LIPS", "right_table": "LIKP", "join_type": "LEFT", "condition": "LIPS.MANDT = LIKP.MANDT\n        AND LIPS.VBELN = LIKP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}], "filter_count": 2, "join_count": 4, "warnings": []}], "summary_report": {"summary": {"total_files": 4, "successful_extractions": 4, "failed_extractions": 0, "success_rate": 100.0, "unique_tables": 32, "total_parameters": 9, "average_complexity": 34.0}, "table_consolidation": {"DUMMY": {"procedures": [""], "procedure_count": 1}, "KNA1": {"procedures": [""], "procedure_count": 1}, "KNB1": {"procedures": [""], "procedure_count": 1}, "MARA": {"procedures": [""], "procedure_count": 1}, "VBAK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "LIKP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTK": {"procedures": [""], "procedure_count": 1}, "VBRK": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BKPF": {"procedures": [""], "procedure_count": 1}, "BSID": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSAD": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "CDHDR": {"procedures": [""], "procedure_count": 1}, "SALES": {"procedures": [""], "procedure_count": 1}, "BILLING": {"procedures": [""], "procedure_count": 1}, "KONV_DATA": {"procedures": [""], "procedure_count": 1}, "VBAP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUP": {"procedures": [""], "procedure_count": 1}, "LIPS": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTP": {"procedures": [""], "procedure_count": 1}, "VBRP": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSEG": {"procedures": ["", "PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 2}, "VBFA": {"procedures": [""], "procedure_count": 1}, "CDPOS": {"procedures": [""], "procedure_count": 1}, "KONV": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "CT_BILLING_PAYMENT_ANALYSIS": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_PAYMENT_STATISTICS": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "PAYMENT": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_BSEG_DELTA_ACTIVITIES": {"procedures": ["PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 1}, "CT_ORDER_DELIVERY_ANALYSIS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CT_PROCESS_STATISTICS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "STATISTICS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}}, "successful_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 25, "parameter_count": 0, "complexity_score": 63, "parsing_method": "FALLBACK", "tables": ["DUMMY", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV"], "table_details": {"DUMMY": {"columns": [], "usage_context": ["SELECT"], "alias": null, "schema": null}, "KNA1": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "KNB1": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "MARA": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VBAK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "LIKP": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VTTK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "VBRK": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BKPF": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BSID": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "BSAD": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "CDHDR": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "SALES": {"columns": [], "usage_context": ["SELECT"], "alias": "Orders", "schema": null}, "BILLING": {"columns": [], "usage_context": ["SELECT"], "alias": "Documents", "schema": null}, "KONV_DATA": {"columns": [], "usage_context": ["SELECT"], "alias": null, "schema": null}, "VBAP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBUK": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBUP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "LIPS": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VTTP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBRP": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "BSEG": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "VBFA": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "CDPOS": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}, "KONV": {"columns": [], "usage_context": ["SELECT"], "alias": "T2", "schema": null}}, "parameters": [], "filters": [{"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "TPLST", "operator": "=", "values": [":IP_TPLST"], "condition_text": "T1.TPLST = :IP_TPLST", "is_parameter": true}, {"table": "T1", "column": "BUKRS_VF", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "T1.BUKRS_VF = :IP_BUKRS", "is_parameter": true}, {"table": "T2", "column": "BUKRS", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "T2.BUKRS = :IP_BUKRS", "is_parameter": true}, {"table": "T1", "column": "BLART", "operator": "IN", "values": ["RV", "DR", "DZ"], "condition_text": "T1.BLART IN RV, DR, DZ", "is_parameter": false}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "OBJECTCLAS", "operator": "IN", "values": ["VERKBELEG", "LIEFERUNG", "FAKTURA"], "condition_text": "T1.OBJECTCLAS IN VERKBELEG, LIEFERUNG, FAKTURA", "is_parameter": false}, {"table": "T1", "column": "TCODE", "operator": "LIKE", "values": [":IP_TCODE"], "condition_text": "T1.TCODE LIKE :IP_TCODE", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "T1", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "T1.VKORG = :IP_VKORG", "is_parameter": true}], "joins": [], "filter_count": 13, "join_count": 0, "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 33, "parsing_method": "FALLBACK", "tables": ["VBRK", "CT_BILLING_PAYMENT_ANALYSIS", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATISTICS", "PAYMENT"], "table_details": {"VBRK": {"columns": ["FKART", "VBELN", "BUKRS", "KNUMV", "VGBEL", "FKDAT", "ZTERM", "MANDT", "KUNAG"], "usage_context": ["SELECT"], "alias": "INNER", "schema": null}, "CT_BILLING_PAYMENT_ANALYSIS": {"columns": [], "usage_context": ["SELECT", "TRUNCATE", "INSERT"], "alias": null, "schema": null}, "VBRP": {"columns": ["NETWR", "MANDT", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "BSID": {"columns": ["KUNNR", "MANDT", "BELNR", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "BSAD": {"columns": ["KUNNR", "AUGDT", "MANDT", "BELNR", "AUGBL"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "KONV": {"columns": ["KWERT", "MANDT", "KSCHL", "KNUMV"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "CT_PAYMENT_STATISTICS": {"columns": [], "usage_context": ["INSERT"], "alias": null, "schema": null}, "PAYMENT": {"columns": [], "usage_context": ["UPDATE"], "alias": null, "schema": null}}, "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "filters": [{"table": "VBRK", "column": "BUKRS", "operator": "=", "values": [":IP_BUKRS"], "condition_text": "VBRK.BUKRS = :IP_BUKRS", "is_parameter": true}, {"table": "VBRK", "column": "FKART", "operator": "IN", "values": ["F2", "G2", "S1"], "condition_text": "VBRK.FKART IN F2, G2, S1", "is_parameter": false}], "joins": [{"left_table": "VBRK", "right_table": "VBRP", "join_type": "INNER", "condition": "VBRK.MANDT = VBRP.MANDT\n        AND VBRK.VBELN = VBRP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBRK", "right_table": "BSID", "join_type": "LEFT", "condition": "VBRK.MANDT = BSID.MANDT\n        AND VBRK.VBELN = BSID.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "BSID", "right_table": "BSAD", "join_type": "LEFT", "condition": "BSID.MANDT = BSAD.MANDT\n        AND BSID.KUNNR = BSAD.KUNNR\n        AND BSID.BELNR = BSAD.BELNR", "left_columns": ["MANDT", "KUNNR", "BELNR"], "right_columns": ["MANDT", "KUNNR", "BELNR"]}, {"left_table": "VBRK", "right_table": "KONV", "join_type": "LEFT", "condition": "VBRK.MANDT = KONV.MANDT\n        AND VBRK.KNUMV = KONV.KNUMV", "left_columns": ["MANDT", "KNUMV"], "right_columns": ["MANDT", "KNUMV"]}], "filter_count": 2, "join_count": 4, "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 2, "parameter_count": 3, "complexity_score": 7, "parsing_method": "FALLBACK", "tables": ["BSEG", "CT_BSEG_DELTA_ACTIVITIES"], "table_details": {"BSEG": {"columns": [], "usage_context": ["SELECT"], "alias": "WHERE", "schema": null}, "CT_BSEG_DELTA_ACTIVITIES": {"columns": [], "usage_context": ["TRUNCATE", "INSERT"], "alias": null, "schema": null}}, "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "filters": [], "joins": [], "filter_count": 0, "join_count": 0, "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 33, "parsing_method": "FALLBACK", "tables": ["VBAK", "CT_ORDER_DELIVERY_ANALYSIS", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATISTICS", "STATISTICS"], "table_details": {"VBAK": {"columns": ["AUART", "VBELN", "VKORG", "MANDT", "ERDAT"], "usage_context": ["SELECT"], "alias": "INNER", "schema": null}, "CT_ORDER_DELIVERY_ANALYSIS": {"columns": [], "usage_context": ["SELECT", "TRUNCATE", "INSERT"], "alias": null, "schema": null}, "VBAP": {"columns": ["VBELN", "MANDT", "MATNR", "NETWR", "POSNR"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "VBUK": {"columns": ["GBSTK", "MANDT", "VBELN", "ABSTK"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "LIPS": {"columns": ["VBELN", "VGPOS", "VGBEL", "MANDT", "LFIMG"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "LIKP": {"columns": ["WBSTK", "MANDT", "WADAT", "VBELN"], "usage_context": ["SELECT"], "alias": null, "schema": null}, "CT_PROCESS_STATISTICS": {"columns": [], "usage_context": ["INSERT"], "alias": null, "schema": null}, "STATISTICS": {"columns": [], "usage_context": ["UPDATE"], "alias": null, "schema": null}}, "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "filters": [{"table": "VBAK", "column": "VKORG", "operator": "=", "values": [":IP_VKORG"], "condition_text": "VBAK.VKORG = :IP_VKORG", "is_parameter": true}, {"table": "VBAK", "column": "AUART", "operator": "IN", "values": ["OR", "TA", "ZOR"], "condition_text": "VBAK.AUART IN OR, TA, ZOR", "is_parameter": false}], "joins": [{"left_table": "VBAK", "right_table": "VBAP", "join_type": "INNER", "condition": "VBAK.MANDT = VBAP.MANDT\n        AND VBAK.VBELN = VBAP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBAK", "right_table": "VBUK", "join_type": "INNER", "condition": "VBAK.MANDT = VBUK.MANDT\n        AND VBAK.VBELN = VBUK.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}, {"left_table": "VBAP", "right_table": "LIPS", "join_type": "LEFT", "condition": "VBAP.MANDT = LIPS.MANDT\n        AND VBAP.VBELN = LIPS.VGBEL\n        AND VBAP.POSNR = LIPS.VGPOS", "left_columns": ["MANDT", "VBELN", "POSNR"], "right_columns": ["MANDT", "VGBEL", "VGPOS"]}, {"left_table": "LIPS", "right_table": "LIKP", "join_type": "LEFT", "condition": "LIPS.MANDT = LIKP.MANDT\n        AND LIPS.VBELN = LIKP.VBELN", "left_columns": ["MANDT", "VBELN"], "right_columns": ["MANDT", "VBELN"]}], "filter_count": 2, "join_count": 4, "warnings": []}], "failed_results": []}, "timestamp": "1749724099.5959895"}