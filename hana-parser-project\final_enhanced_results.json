{"extraction_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 25, "parameter_count": 0, "complexity_score": 53, "parsing_method": "FALLBACK", "tables": ["DUMMY", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 32, "parsing_method": "FALLBACK", "tables": ["VBRK", "CT_BILLING_PAYMENT_ANALYSIS", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATISTICS", "PAYMENT"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 2, "parameter_count": 3, "complexity_score": 7, "parsing_method": "FALLBACK", "tables": ["BSEG", "CT_BSEG_DELTA_ACTIVITIES"], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 32, "parsing_method": "FALLBACK", "tables": ["VBAK", "CT_ORDER_DELIVERY_ANALYSIS", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATISTICS", "STATISTICS"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "warnings": []}], "summary_report": {"summary": {"total_files": 4, "successful_extractions": 4, "failed_extractions": 0, "success_rate": 100.0, "unique_tables": 32, "total_parameters": 9, "average_complexity": 31.0}, "table_consolidation": {"DUMMY": {"procedures": [""], "procedure_count": 1}, "KNA1": {"procedures": [""], "procedure_count": 1}, "KNB1": {"procedures": [""], "procedure_count": 1}, "MARA": {"procedures": [""], "procedure_count": 1}, "VBAK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "LIKP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTK": {"procedures": [""], "procedure_count": 1}, "VBRK": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BKPF": {"procedures": [""], "procedure_count": 1}, "BSID": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSAD": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "CDHDR": {"procedures": [""], "procedure_count": 1}, "SALES": {"procedures": [""], "procedure_count": 1}, "BILLING": {"procedures": [""], "procedure_count": 1}, "KONV_DATA": {"procedures": [""], "procedure_count": 1}, "VBAP": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUK": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VBUP": {"procedures": [""], "procedure_count": 1}, "LIPS": {"procedures": ["", "PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 2}, "VTTP": {"procedures": [""], "procedure_count": 1}, "VBRP": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "BSEG": {"procedures": ["", "PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 2}, "VBFA": {"procedures": [""], "procedure_count": 1}, "CDPOS": {"procedures": [""], "procedure_count": 1}, "KONV": {"procedures": ["", "PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 2}, "CT_BILLING_PAYMENT_ANALYSIS": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_PAYMENT_STATISTICS": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "PAYMENT": {"procedures": ["PR_BILLING_PAYMENT_ANALYSIS"], "procedure_count": 1}, "CT_BSEG_DELTA_ACTIVITIES": {"procedures": ["PR_BSEG_ACTIVITY_LOAD"], "procedure_count": 1}, "CT_ORDER_DELIVERY_ANALYSIS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "CT_PROCESS_STATISTICS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}, "STATISTICS": {"procedures": ["PR_ORDER_DELIVERY_ANALYSIS"], "procedure_count": 1}}, "successful_results": [{"file": "..\\sample_procedures\\O2C.sql", "success": true, "procedure_name": "", "table_count": 25, "parameter_count": 0, "complexity_score": 53, "parsing_method": "FALLBACK", "tables": ["DUMMY", "KNA1", "KNB1", "MARA", "VBAK", "LIKP", "VTTK", "VBRK", "BKPF", "BSID", "BSAD", "CDHDR", "SALES", "BILLING", "KONV_DATA", "VBAP", "VBUK", "VBUP", "LIPS", "VTTP", "VBRP", "BSEG", "VBFA", "CDPOS", "KONV"], "parameters": [], "warnings": []}, {"file": "..\\sample_procedures\\PR_BILLING_PAYMENT_ANALYSIS.sql", "success": true, "procedure_name": "PR_BILLING_PAYMENT_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 32, "parsing_method": "FALLBACK", "tables": ["VBRK", "CT_BILLING_PAYMENT_ANALYSIS", "VBRP", "BSID", "BSAD", "KONV", "CT_PAYMENT_STATISTICS", "PAYMENT"], "parameters": ["IN IP_FKDAT_FROM DATE", "IN IP_FKDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_BSEG_ACTIVITY_LOAD.sql", "success": true, "procedure_name": "PR_BSEG_ACTIVITY_LOAD", "table_count": 2, "parameter_count": 3, "complexity_score": 7, "parsing_method": "FALLBACK", "tables": ["BSEG", "CT_BSEG_DELTA_ACTIVITIES"], "parameters": ["IN IP_BUDAT_FROM DATE", "IN IP_BUDAT_TO DATE", "IN IP_BUKRS VARCHAR(4"], "warnings": []}, {"file": "..\\sample_procedures\\PR_ORDER_DELIVERY_ANALYSIS.sql", "success": true, "procedure_name": "PR_ORDER_DELIVERY_ANALYSIS", "table_count": 8, "parameter_count": 3, "complexity_score": 32, "parsing_method": "FALLBACK", "tables": ["VBAK", "CT_ORDER_DELIVERY_ANALYSIS", "VBAP", "VBUK", "LIPS", "LIKP", "CT_PROCESS_STATISTICS", "STATISTICS"], "parameters": ["IN IP_ERDAT_FROM DATE", "IN IP_ERDAT_TO DATE", "IN IP_VKORG VARCHAR(4"], "warnings": []}], "failed_results": []}, "timestamp": "1749718598.5678203"}