"""
LLM-Based SAP HANA Table Analyzer - Command Line Interface
==========================================================

Command-line interface for analyzing SAP HANA procedures using GitHub Models LLM endpoint.

Usage:
    python main.py --file procedure.sql
    python main.py --directory /path/to/procedures/
    python main.py --streamlit  # Launch Streamlit web app
"""

import argparse
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Add src directory to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from llm_analyzer import GitHubModelsLLMAnalyzer, ProcedureLoader, AnalysisResult


def analyze_single_file(file_path: str, analyzer: GitHubModelsLLMAnalyzer) -> bool:
    """Analyze a single file"""
    
    print(f"📁 Analyzing: {file_path}")
    
    try:
        procedures = ProcedureLoader.load_from_files([file_path])
        
        if not procedures:
            print(f"❌ No procedures loaded from {file_path}")
            return False
        
        result = analyzer.analyze_procedures(procedures)
        
        if result.success:
            print(f"✅ Success - Found {result.total_tables} tables")
            return True
        else:
            print(f"❌ Failed: {result.error}")
            return False
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def analyze_directory(directory_path: str, analyzer: GitHubModelsLLMAnalyzer) -> AnalysisResult:
    """Analyze all procedures in a directory"""
    
    print(f"📁 Analyzing directory: {directory_path}")
    
    try:
        procedures = ProcedureLoader.load_from_directory(directory_path)
        
        if not procedures:
            print(f"❌ No procedures found in {directory_path}")
            return AnalysisResult(success=False, error="No procedures found")
        
        print(f"🔍 Found {len(procedures)} procedures")
        
        # Limit to avoid token limits
        if len(procedures) > 10:
            print(f"⚠️ Too many procedures. Limiting to first 10 procedures.")
            procedures = procedures[:10]
        
        result = analyzer.analyze_procedures(procedures)
        
        if result.success:
            print(f"✅ Success - Analyzed {result.procedure_count} procedures")
            print(f"📊 Found {result.total_tables} unique tables")
        else:
            print(f"❌ Failed: {result.error}")
        
        return result
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return AnalysisResult(success=False, error=str(e))


def display_results(result: AnalysisResult):
    """Display analysis results"""
    
    if not result.success or not result.table_analysis:
        return
    
    print("\n" + "=" * 80)
    print("📊 TABLE ANALYSIS RESULTS")
    print("=" * 80)
    
    for i, table_data in enumerate(result.table_analysis, 1):
        print(f"\n{i}. {table_data.get('Table Name', 'Unknown')}")
        print(f"   Used in Procedures: {table_data.get('Used in Procedures', '')}")
        print(f"   Procedure Count: {table_data.get('Procedure Count', 0)}")
        print(f"   All Columns: {table_data.get('All Columns', '')}")
        print(f"   Usage Contexts: {table_data.get('Usage Contexts', '')}")
        
        filter_patterns = table_data.get('Filter Patterns', '')
        if filter_patterns:
            print(f"   Filter Patterns: {filter_patterns}")
        
        join_relationships = table_data.get('Join Relationships', '')
        if join_relationships:
            print(f"   Join Relationships: {join_relationships}")
    
    print(f"\n📈 Summary:")
    print(f"   Total Tables: {result.total_tables}")
    print(f"   Procedures Analyzed: {result.procedure_count}")


def save_results(result: AnalysisResult, output_file: str):
    """Save results to file"""
    
    if not result.success:
        print(f"❌ Cannot save failed analysis results")
        return
    
    try:
        output_data = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'procedure_count': result.procedure_count,
                'total_tables': result.total_tables,
                'success': result.success
            },
            'table_analysis': result.table_analysis,
            'raw_llm_response': result.raw_response
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: {output_file}")
    
    except Exception as e:
        print(f"❌ Error saving results: {e}")


def test_llm_connection(token: str) -> bool:
    """Test LLM connection"""
    
    print("🔍 Testing LLM connection...")
    
    try:
        analyzer = GitHubModelsLLMAnalyzer(token)
        
        if analyzer.test_connection():
            print("✅ LLM connection successful!")
            return True
        else:
            print("❌ LLM connection failed")
            return False
    
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False


def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(
        description="LLM-Based SAP HANA Table Analyzer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --file procedure.sql
  python main.py --directory ../sample_procedures/
  python main.py --streamlit
  python main.py --directory ../sample_procedures/ --output results.json
  python main.py --test-connection
        """
    )
    
    parser.add_argument('--file', '-f', help='Analyze a single SQL file')
    parser.add_argument('--directory', '-d', help='Analyze all SQL files in a directory')
    parser.add_argument('--streamlit', '-s', action='store_true', help='Launch Streamlit web application')
    parser.add_argument('--output', '-o', help='Output file for results (JSON format)')
    parser.add_argument('--token', '-t', help='GitHub token (or set GITHUB_TOKEN env var)')
    parser.add_argument('--test-connection', action='store_true', help='Test LLM connection')
    
    args = parser.parse_args()
    
    # Check if no arguments provided
    if not any([args.file, args.directory, args.streamlit, args.test_connection]):
        parser.print_help()
        return
    
    # Get token
    token = args.token or os.getenv('GITHUB_TOKEN') or "****************************************"
    
    # Test connection
    if args.test_connection:
        test_llm_connection(token)
        return
    
    # Launch Streamlit app
    if args.streamlit:
        print("🚀 Launching Streamlit web application...")
        os.system("streamlit run app.py")
        return
    
    # Initialize analyzer
    print("🤖 Initializing LLM-based analyzer...")
    analyzer = GitHubModelsLLMAnalyzer(token)
    
    # Test connection first
    if not analyzer.test_connection():
        print("❌ LLM connection failed. Please check your token.")
        return
    
    result = None
    
    # Analyze single file
    if args.file:
        success = analyze_single_file(args.file, analyzer)
        if success:
            # For single file, create a simple result
            procedures = ProcedureLoader.load_from_files([args.file])
            result = analyzer.analyze_procedures(procedures)
    
    # Analyze directory
    elif args.directory:
        result = analyze_directory(args.directory, analyzer)
    
    # Display and save results
    if result:
        display_results(result)
        
        if args.output:
            save_results(result, args.output)


if __name__ == "__main__":
    main()
