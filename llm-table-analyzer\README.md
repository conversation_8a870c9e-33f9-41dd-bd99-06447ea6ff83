# LLM-Based SAP HANA Table Analyzer

A pure LLM-based tool for analyzing SAP HANA SQL procedures and generating detailed table usage metadata using GitHub Models endpoint.

## 🚀 Features

- **Pure LLM Analysis**: Uses GitHub Models (gpt-4o) for intelligent SQL analysis
- **No Parser Dependencies**: No ANTLR or complex parsing required
- **Exact Table Format**: Generates the exact table metadata format you need
- **Streamlit Web Interface**: User-friendly web application
- **Command Line Interface**: Batch processing capabilities
- **GitHub Models Integration**: Uses the specific endpoint and model you requested

## 📋 Enhanced Output Format

Generates intelligent table/view analysis with these columns:

| Column | Description |
|--------|-------------|
| **Table Name** | Full qualified name (SCHEMA.TABLE or VIEW) |
| **Object Type** | TABLE, VIEW, CDS_VIEW, CALCULATION_VIEW |
| **Schema** | Schema name (_SYS_BIC, SAP_SCHEMA, etc.) |
| **Used in Procedures** | Comma-separated list of procedures |
| **Procedure Count** | Number of procedures using this object |
| **All Columns** | All columns referenced (TABLE.COLUMN format) |
| **Usage Contexts** | How object is used (FILTER, JOIN, SELECT, etc.) |
| **Filter Patterns** | Intelligent per-procedure filter analysis |
| **Recommended Filter Strategy** | USE_ALL_VALUES or USE_RESTRICTIVE_FILTERS |
| **Filter Conflict Analysis** | Detailed conflict analysis and recommendations |
| **Join Relationships** | Detailed join conditions (→ TABLE: condition) |

## 🧠 Intelligent Filter Analysis

The enhanced LLM analyzer provides **smart filter conflict detection**:

### Filter Pattern Format
```
COLUMN: [PROC1: condition1, PROC2: ALL_VALUES] | COLUMN2: [ALL_PROCS: condition2]
```

### Examples
- `MANDT: [PR_CLIENT_100: ='100', PR_ALL_CLIENTS: ALL_VALUES]`
- `STATUS: [ALL_PROCS: IN('ACTIVE','PENDING')]`
- `DATE: [PR_RECENT: >=2024-01-01, PR_HISTORICAL: ALL_VALUES]`

### Intelligent Recommendations
- **USE_ALL_VALUES**: When procedures have conflicting filter needs
- **USE_RESTRICTIVE_FILTERS**: When all procedures use consistent filters
- **Conflict Analysis**: Detailed explanation of why recommendation was made

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- GitHub token for accessing models endpoint

### Setup

1. **Clone or download the project**
   ```bash
   cd llm-table-analyzer
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up GitHub token** (optional - default token is included)
   ```bash
   export GITHUB_TOKEN=your_github_token_here
   ```

## 🖥️ Usage

### Streamlit Web Application

Launch the web interface:
```bash
streamlit run app.py
```

#### Web Interface Features:
- **Upload & Analyze**: Upload SQL files or paste SQL text
- **LLM Analysis**: Uses GitHub Models endpoint for analysis
- **Table Analysis**: Shows detailed table metadata in exact format
- **Visualizations**: Interactive charts and graphs
- **CSV Export**: Download results in CSV format

### Command Line Interface

#### Test LLM connection:
```bash
python main.py --test-connection
```

#### Analyze a single file:
```bash
python main.py --file procedure.sql
```

#### Analyze all files in a directory:
```bash
python main.py --directory /path/to/procedures/
```

#### Save results to JSON:
```bash
python main.py --directory /path/to/procedures/ --output results.json
```

#### Launch Streamlit from CLI:
```bash
python main.py --streamlit
```

### Python API

```python
from src.llm_analyzer import GitHubModelsLLMAnalyzer, ProcedureLoader

# Initialize analyzer
analyzer = GitHubModelsLLMAnalyzer()

# Load procedures
procedures = ProcedureLoader.load_from_files(['procedure.sql'])

# Analyze
result = analyzer.analyze_procedures(procedures)

if result.success:
    print(f"Found {result.total_tables} tables")
    for table_data in result.table_analysis:
        print(f"Table: {table_data['Table Name']}")
        print(f"Columns: {table_data['All Columns']}")
```

## 🔧 Configuration

### GitHub Models Endpoint

The analyzer uses these exact settings as requested:

```python
endpoint = "https://models.inference.ai.azure.com"
model = "gpt-4o"
token = "****************************************"  # Default included
```

### LLM Parameters

- **Temperature**: 0.1 (low for consistent output)
- **Top P**: 0.9
- **Max Tokens**: 4000
- **Model**: gpt-4o

## 📊 Sample Output

```json
[
  {
    "Table Name": "VBRK",
    "Used in Procedures": "PR_BILLING_ANALYSIS, PR_INVOICE_REPORT",
    "Procedure Count": 2,
    "All Columns": "VBRK.VBELN, VBRK.FKDAT, VBRK.KUNAG, VBRK.NETWR",
    "Usage Contexts": "FILTER, JOIN, SELECT",
    "Filter Patterns": "FKDAT: VBRK.FKDAT BETWEEN :IP_DATE_FROM AND :IP_DATE_TO | FKART: VBRK.FKART IN ('F2', 'G2')",
    "Join Relationships": "→ VBRP: VBRK.MANDT = VBRP.MANDT AND VBRK.VBELN = VBRP.VBELN | → KNA1: VBRK.KUNAG = KNA1.KUNNR"
  }
]
```

## 🏗️ Architecture

### Core Components

1. **GitHubModelsLLMAnalyzer**: Main LLM analysis engine
2. **ProcedureLoader**: Utility for loading SQL procedures
3. **Streamlit App**: Web interface
4. **CLI Interface**: Command-line tools

### LLM Analysis Process

1. **Prompt Engineering**: Carefully crafted prompts for consistent output
2. **JSON Parsing**: Robust parsing of LLM responses
3. **Data Validation**: Ensures output matches required format
4. **Error Handling**: Graceful handling of LLM failures

## 📁 Project Structure

```
llm-table-analyzer/
├── src/
│   └── llm_analyzer.py      # Core LLM analysis logic
├── examples/
│   └── example_usage.py     # Usage examples
├── app.py                   # Streamlit web application
├── main.py                  # Command-line interface
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## 🎯 Advantages of LLM Approach

- **No Parser Complexity**: No need for ANTLR grammar or complex parsing
- **Intelligent Analysis**: LLM understands SQL semantics and relationships
- **Flexible Input**: Handles various SQL dialects and formats
- **Rich Metadata**: Extracts detailed filter patterns and join relationships
- **Easy Maintenance**: No grammar files or parser rules to maintain

## 🔍 Examples

### Basic Usage
```bash
# Test connection
python main.py --test-connection

# Analyze sample procedures
python main.py --directory ../sample_procedures/ --output results.json

# Launch web interface
python main.py --streamlit
```

### Web Interface
1. Open browser to Streamlit app
2. Upload SQL procedure files
3. Click "Start LLM Analysis"
4. View results in "Table Analysis" tab
5. Download CSV with detailed metadata

## 🚨 Limitations

- **Token Limits**: Large procedures may hit LLM token limits
- **Cost**: LLM API calls have associated costs
- **Internet Required**: Requires connection to GitHub Models endpoint
- **Rate Limits**: Subject to GitHub Models rate limiting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with sample procedures
5. Submit a pull request

## 📝 License

This project is provided as-is for educational and development purposes.

## 🐛 Troubleshooting

### Common Issues

1. **LLM Connection Failed**
   - Check GitHub token validity
   - Verify internet connection
   - Test with `--test-connection` flag

2. **Token Limit Exceeded**
   - Reduce number of procedures
   - Split large procedures into smaller batches
   - Use shorter procedure content

3. **Invalid JSON Response**
   - LLM occasionally returns malformed JSON
   - Retry the analysis
   - Check procedure content for unusual characters

### Getting Help

- Run examples in `examples/example_usage.py`
- Use `--test-connection` to verify setup
- Check LLM response in Streamlit "Analysis Results" tab

## 📈 Performance

- **Analysis Speed**: ~10-30 seconds per batch (depending on LLM response time)
- **Accuracy**: High accuracy for standard SAP tables and SQL patterns
- **Scalability**: Limited by LLM token limits (~10-20 procedures per batch)

## 🎯 Use Cases

- **Migration Planning**: Analyze table dependencies without complex parsing
- **Quick Analysis**: Fast analysis of procedure collections
- **Prototype Development**: Rapid development without parser setup
- **Documentation**: Generate table usage documentation
- **Impact Analysis**: Understand cross-procedure relationships
