"""
Demo: Enhanced Table Analysis Format
===================================

This demonstrates the enhanced table analysis format with Filter Patterns 
and Join Relationships as requested.
"""

import pandas as pd
from datetime import datetime

def create_demo_table_analysis():
    """Create a demo table analysis in the requested format"""
    
    # Sample data in the format you requested
    demo_data = [
        {
            'Table Name': 'VBRP',
            'Used in Procedures': 'PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure',
            'Procedure Count': 5,
            'All Columns': 'VBRP.AUBEL, VBRP.AUPOS, VBRP.MANDT, VBRP.POSNR, VBRP.VBELN',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': '',
            'Join Relationships': '→ VBRP: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → VBAP: 1=1 AND VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR | → VBFA: 1=1 AND VBFA.MANDT = VBRP.MANDT AND VBFA.VBELN = VBRP.VBELN AND VBFA.POSNN = VBRP.POSNR | → VBRK: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN'
        },
        {
            'Table Name': 'VBAP',
            'Used in Procedures': 'PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure',
            'Procedure Count': 5,
            'All Columns': 'VBAP.ERDAT, VBAP.MANDT, VBAP.POSNR, VBAP.VBELN',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': '',
            'Join Relationships': '→ VBRP: 1=1 AND VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR | → VBAK: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = \'C\' AND VBAP.ERDAT >= :IP_ERDAT | → VBFA: 1=1 AND VBAP.MANDT = VBFA.MANDT AND VBAP.VBELN = VBFA.VBELV AND VBAP.POSNR = VBFA.POSNV'
        },
        {
            'Table Name': 'VBRK',
            'Used in Procedures': 'PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure',
            'Procedure Count': 5,
            'All Columns': 'VBRK.MANDT, VBRK.VBELN',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': '',
            'Join Relationships': '→ VBRK: 1=1 AND VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY | → VBRP: 1=1 AND VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN | → BKPF: 1=1 AND VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY AND BKPF_INV.AWTYP = \'VBRK\' | → WO: 1=1 AND WO.INSTID = VBRK.VBELN'
        },
        {
            'Table Name': 'BSEG',
            'Used in Procedures': 'PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure',
            'Procedure Count': 4,
            'All Columns': '',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': '',
            'Join Relationships': '→ BSEG_INV: 1=1 AND BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.BUKRS = SUBSTRING(CDPOS.OBJECTID,4,4) AND BSEG_INV.BELNR = SUBSTRING(CDPOS.OBJECTID,8,10) AND BSEG_INV.GJAHR = SUBSTRING(CDPOS.OBJECTID,18,4) AND BSEG_INV.BSCHL IN (\'01\',\'11\') AND BSEG_INV.KOART = \'D\' | → BKPF_INV: 1=1 AND BKPF_INV.MANDT = BSEG_INV.MANDT AND BKPF_INV.BUKRS = BSEG_INV.BUKRS AND BKPF_INV.BELNR = BSEG_INV.BELNR AND BKPF_INV.GJAHR = BSEG_INV.GJAHR AND BSEG_INV.BSCHL IN (\'01\',\'11\') AND BSEG_INV.KOART = \'D\' AND BKPF_INV.AWTYP = \'VBRK\''
        },
        {
            'Table Name': 'VBAK',
            'Used in Procedures': 'PR_GS_O2C_ACCL_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure',
            'Procedure Count': 4,
            'All Columns': 'VBAK.CURRENT_UTCTIMESTAMP, VBAK.INVOIVE, VBAK.MANDT, VBAK.VBELN, VBAK.VBTYP',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': '',
            'Join Relationships': '→ VBAK: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = \'C\' | → VBAP: 1=1 AND VBAK.MANDT = VBAP.MANDT AND VBAK.VBELN = VBAP.VBELN AND VBAK.VBTYP = \'C\' AND VBAP.ERDAT >= :IP_ERDAT'
        },
        {
            'Table Name': 'CDPOS',
            'Used in Procedures': 'PR_GS_O2C_ACBD_DELTA.hdbprocedure, PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_ADPB_DELTA.hdbprocedure',
            'Procedure Count': 3,
            'All Columns': 'CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.TABKEY, CDPOS.TABNAME, CDPOS.VALUE_NEW, CDPOS.VALUE_OLD',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': 'FNAME: CDPOS.FNAME IN (\'ZFBDT\',\'ZBD1T\') | VALUE_NEW: CDPOS.VALUE_NEW <> \'\'',
            'Join Relationships': '→ CDPOS: 1=1 AND CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR AND CDPOS.FNAME = \'ZFBDT\' AND CDPOS.TABNAME = \'BSEG\''
        },
        {
            'Table Name': 'VBFA',
            'Used in Procedures': 'PR_GS_O2C_ACDD_DELTA.hdbprocedure, PR_GS_O2C_AIRWF_DELTA.hdbprocedure',
            'Procedure Count': 2,
            'All Columns': 'VBFA.MANDT, VBFA.POSNN, VBFA.POSNV, VBFA.VBELN, VBFA.VBELV, VBFA.VBTYP_N, VBFA.VBTYP_V',
            'Usage Contexts': 'FILTER, JOIN, SELECT',
            'Filter Patterns': 'VBTYP_N: VBFA.VBTYP_N = \'3\'',
            'Join Relationships': '→ VBAP: 1=1 AND VBAP.MANDT = VBFA.MANDT AND VBAP.VBELN = VBFA.VBELV AND VBAP.POSNR = VBFA.POSNV | → VBRP: 1=1 AND VBFA.MANDT = VBRP.MANDT AND VBFA.VBELN = VBRP.VBELN AND VBFA.POSNN = VBRP.POSNR | → VBFA: 1=1 AND VBFA.MANDT = VBRK.MANDT AND VBFA.VBELN = VBRK.VBELN AND VBFA.VBTYP_N = \'3\' AND VBFA.VBTYP_V = \'M\''
        }
    ]
    
    return pd.DataFrame(demo_data)


def display_demo():
    """Display the demo table analysis"""
    
    print("🔍 SAP HANA Enhanced Table Analysis - Demo Format")
    print("=" * 80)
    print("This shows the enhanced format with Filter Patterns and Join Relationships")
    print("=" * 80)
    
    df = create_demo_table_analysis()
    
    # Display each row in a readable format
    for index, row in df.iterrows():
        print(f"\n📋 {index + 1}. {row['Table Name']}")
        print(f"   Used in Procedures: {row['Used in Procedures']}")
        print(f"   Procedure Count: {row['Procedure Count']}")
        print(f"   All Columns: {row['All Columns']}")
        print(f"   Usage Contexts: {row['Usage Contexts']}")
        
        if row['Filter Patterns']:
            print(f"   Filter Patterns: {row['Filter Patterns']}")
        else:
            print(f"   Filter Patterns: (none)")
        
        if row['Join Relationships']:
            print(f"   Join Relationships: {row['Join Relationships']}")
        else:
            print(f"   Join Relationships: (none)")
    
    print(f"\n📊 Summary:")
    print(f"   Total Tables: {len(df)}")
    print(f"   Tables with Filters: {len(df[df['Filter Patterns'] != ''])}")
    print(f"   Tables with Joins: {len(df[df['Join Relationships'] != ''])}")
    
    # Save to CSV to show the exact format
    csv_filename = f"demo_table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(csv_filename, index=False)
    print(f"\n💾 Demo data saved to: {csv_filename}")
    
    return df


def show_streamlit_integration():
    """Show how this would integrate with the Streamlit app"""
    
    print("\n" + "=" * 80)
    print("🖥️ STREAMLIT INTEGRATION")
    print("=" * 80)
    
    print("""
The enhanced HANA parser project now includes:

1. **Enhanced Data Models**: 
   - FilterCondition with detailed condition text
   - JoinCondition with left/right table relationships
   - Enhanced table consolidation with filter patterns and join relationships

2. **Streamlit Interface Updates**:
   - Table Analysis tab shows the enhanced format
   - Filter Patterns column with detailed filter conditions
   - Join Relationships column with detailed join conditions
   - Same CSV export functionality

3. **Column Structure** (matching your request):
   | Table Name | Used in Procedures | All Columns | Filter Patterns | Join Relationships |
   
4. **Data Format**:
   - Filter Patterns: "column: condition | column: condition"
   - Join Relationships: "→ TABLE: condition | → TABLE: condition"
   - All Columns: "TABLE.COLUMN, TABLE.COLUMN"

5. **Usage**:
   ```bash
   cd hana-parser-project
   streamlit run app.py
   ```
   
   Then:
   - Upload SQL procedures
   - Click "Start Extraction"
   - Go to "Table Analysis" tab
   - View enhanced table metadata
   - Download CSV with all details
    """)


if __name__ == "__main__":
    df = display_demo()
    show_streamlit_integration()
    
    print("\n🎉 Enhanced table analysis format demonstration complete!")
    print("The HANA parser project now supports the exact format you requested.")
